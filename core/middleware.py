import logging
import re
import sys
import traceback

from django.conf import settings
from django.http import Http404, JsonResponse
from django.urls import get_resolver
from django.views.debug import technical_404_response, technical_500_response

from core.models import ActivityLog, ActivityLogType
from core.utils import DetailedHttp404, is_admin

logger = logging.getLogger(__name__)


class CustomErrorHandlerMiddleware:
    """
    Middleware that ensures custom error handlers are called even in development mode.
    This middleware uses both the __call__ method and process_exception to ensure
    it catches all exceptions.
    """

    def __init__(self, get_response):
        self.get_response = get_response
        # One-time configuration and initialization
        self.handler404 = None
        self.handler500 = None
        self._load_handlers()
        logger.info("CustomErrorHandlerMiddleware initialized")

    def _load_handlers(self):
        # Get the handlers from the root URLconf
        resolver = get_resolver()
        urlconf = resolver.urlconf_name
        try:
            if urlconf is not None:
                module = __import__(urlconf, fromlist=["handler404", "handler500"])
                self.handler404 = getattr(module, "handler404", None)
                self.handler500 = getattr(module, "handler500", None)
                logger.info(f"Loaded handlers: 404={self.handler404}, 500={self.handler500}")
            else:
                self.handler404 = None
                self.handler500 = None
                logger.warning("URLconf is None, no custom handlers loaded")
        except (ImportError, AttributeError) as e:
            error_msg = f"Failed to load handlers: {e}"
            logger.error(error_msg)
            ActivityLog.objects.create(text=error_msg, type=ActivityLogType.ERROR)

    def __call__(self, request):
        try:
            response = self.get_response(request)
            return response
        except Http404 as exc:
            return self._handle_404(request, exc)
        except Exception as exc:
            return self._handle_500(request, exc)

    def process_exception(self, request, exception):
        """
        Process exceptions raised during request processing.
        This method is called by Django when an exception occurs in a view.
        """
        logger.info(f"process_exception called with exception: {exception} of type {type(exception).__name__}")

        # Handle standard Django exceptions
        if isinstance(exception, Http404):
            return self._handle_404(request, exception)

        return self._handle_500(request, exception)

    def _handle_404(self, request, exc):
        # Log the 404 error
        logger.info(f"Handling 404 error: {exc} for path: {request.path}")

        # Check if this is an API request
        if request.path.startswith("/api/"):
            # For API requests, return a JSON response
            if isinstance(exc, DetailedHttp404):
                data = {"detail": {"message": str(exc), "model": exc.model_name, "params": exc.params}}
                logger.debug(f"Returning DetailedHttp404 JSON response: {data}")
            else:
                data = {"detail": str(exc)}
                logger.debug(f"Returning standard 404 JSON response: {data}")
            return JsonResponse(data, status=404)

        # For regular requests
        if settings.DEBUG and not request.path.startswith("/test/"):
            logger.debug("Using Django's technical_404_response in DEBUG mode")
            return technical_404_response(request, exc)
        if self.handler404:
            handler = self._get_handler(self.handler404)
            logger.debug(f"Calling custom handler404: {handler}")
            return handler(request, exception=exc)

        # If we get here, re-raise the exception
        logger.warning("No handler404 found, re-raising exception")
        raise

    def _handle_500(self, request, exc):
        # Capture traceback info
        tb_string = traceback.format_exc()
        exception_msg = f"Exception caught in middleware: {exc} of type {type(exc).__name__}"
        logger.error(exception_msg)

        traceback_msg = f"Traceback: {tb_string}"
        logger.error(traceback_msg)

        # Prepare context data
        context_data = {
            "path": request.path,
            "method": request.method,
            "user_id": request.user.id if request.user.is_authenticated else None,
            "user_agent": request.META.get("HTTP_USER_AGENT", ""),
            "remote_addr": request.META.get("REMOTE_ADDR", ""),
            "query_params": dict(request.GET.items()),
            "post_params": {
                k: ("[FILTERED]" if re.search(r"(pass(word)?|token|secret|key|auth|session|credit|ssn|email)", k, re.I) else v)
                for k, v in request.POST.items()
            },
            "exception_type": type(exc).__name__,
        }

        # Create a single consolidated ActivityLog entry
        consolidated_msg = f"{exception_msg}\n{traceback_msg}\nContext: {context_data}"
        ActivityLog.objects.create(text=consolidated_msg, type=ActivityLogType.ERROR)

        # Check if this is an API request
        if request.path.startswith("/api/"):
            # For API requests, return a JSON response
            data = {"detail": "Internal Server Error"}
            if settings.DEBUG:
                data["traceback"] = tb_string
                data["exception"] = str(exc)
                data["exception_type"] = type(exc).__name__
            logger.debug(f"Returning API 500 response: {data if not settings.DEBUG else 'with traceback'}")
            return JsonResponse(data, status=500)

        # For regular requests
        if settings.DEBUG and not request.path.startswith("/test/"):
            logger.debug("Using Django's technical_500_response in DEBUG mode")
            exc_type, exc_value, tb = sys.exc_info()
            return technical_500_response(request, exc_type, exc_value, tb)
        if self.handler500:
            handler = self._get_handler(self.handler500)
            # Pass exception and traceback to the handler
            logger.debug(f"Calling handler500 with exception={str(exc)}, traceback length={len(tb_string)}, context keys={context_data.keys()}")
            return handler(request, exception=str(exc), traceback=tb_string, context=context_data)

        # If we get here, re-raise the exception
        logger.warning("No handler500 found, re-raising exception")
        raise exc

    def _get_handler(self, handler):
        if isinstance(handler, str):
            module_name, function_name = handler.rsplit(".", 1)
            module = __import__(module_name, fromlist=[function_name])
            return getattr(module, function_name)
        return handler


class UserMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.user.is_authenticated:
            request.user.is_admin = is_admin(request.user)

        # Get the response
        response = self.get_response(request)

        return response
