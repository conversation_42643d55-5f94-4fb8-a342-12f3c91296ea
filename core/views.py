import json
import logging

from django.conf import settings
from django.contrib import messages
from django.contrib.auth import authenticate
from django.contrib.auth import login as auth_signin
from django.contrib.auth import logout as django_signout
from django.contrib.auth.decorators import login_required
from django.http import HttpRequest, HttpResponse, HttpResponseRedirect
from django.shortcuts import redirect, render
from django.views.decorators.http import require_POST

from core.models import ErrorReport

logger = logging.getLogger(__name__)


def health_check(request: HttpRequest) -> HttpResponse:
    return HttpResponse("Ok!")


def home(request: HttpRequest) -> HttpResponse:
    return render(request, "home.html")


def signin(request: HttpRequest) -> HttpResponse:
    if request.user.is_authenticated:
        return render(request, "home.html")

    if request.method == "POST":
        username = request.POST.get("username")
        password = request.POST.get("password")

        if user := authenticate(request, username=username, password=password):
            auth_signin(request, user)
            return redirect("home")

        messages.error(request, "Invalid username or password")

    return render(request, "signin.html")


@require_POST
@login_required
def signout(request: HttpRequest) -> HttpResponseRedirect:
    django_signout(request)
    return HttpResponseRedirect(settings.LOGOUT_REDIRECT_URL)


@require_POST
def report_error(request: HttpRequest) -> HttpResponse:
    """
    View to handle error report submissions from users.
    """
    error_type = request.POST.get("error_type", "OTHER")
    url = request.POST.get("url", request.build_absolute_uri())
    description = request.POST.get("description", "")
    traceback = request.POST.get("traceback", "")

    context_data = {}
    context_json = request.POST.get("context", "{}")
    try:
        context_data = json.loads(context_json)
    except json.JSONDecodeError:
        logger.warning("Failed to parse context JSON; persisting raw string")
        context_data = {"_unparsed": context_json}

    logger.debug(f"Saving error report with traceback length: {len(traceback)}, context: {context_data}")

    ErrorReport.objects.create(
        error_type=error_type,
        url=url,
        description=description,
        traceback=traceback,
        context=context_data,
        user=request.user if request.user.is_authenticated else None,
    )

    messages.success(request, "Thanks for reporting the error.")

    return redirect("home")


def handler404(request: HttpRequest, exception: Exception = None) -> HttpResponse:
    """
    Custom 404 error handler that displays detailed information about the error.

    If the exception is a DetailedHttp404, it will include model and parameter information.
    """
    context = {
        "error_context": None,
    }

    # If this is our custom DetailedHttp404, add the context
    if hasattr(exception, "model_name") and hasattr(exception, "params"):
        context["error_context"] = {
            "model_name": exception.model_name,
            "params": exception.params,
        }

    return render(request, "404.html", context, status=404)


def handler500(request: HttpRequest, exception=None, traceback=None, context=None) -> HttpResponse:
    """
    Custom 500 error handler that displays information for staff users
    and provides an error reporting form.

    Args:
        request: The HTTP request
        exception: The exception that was raised
        traceback: The traceback string
        context: Additional context data about the request
    """
    # Log the error information for debugging

    # Create a context dictionary with the error information
    context_data = {"exception": exception, "traceback": traceback, "context": context or {}}

    # Add additional debug information
    if settings.DEBUG:
        logger.debug("DEBUG mode is ON, adding extra debug info")
        context_data["debug_info"] = {
            "middleware_classes": settings.MIDDLEWARE,
            "handler500_path": f"{__name__}.handler500",
        }

    return render(request, "500.html", context=context_data, status=500)
