export class HTMXErrorHandler {
  constructor() {
    this.dialogId = 'htmx-error-dialog';
    this.isDialogOpen = false;
    this.currentErrorData = null;

    this.onResponseError = this.handleResponseError.bind(this);
    this.onTimeout = this.handleTimeout.bind(this);
    this.onKeydown = this.handleKeydown.bind(this);

    this.init();
  }

  init() {
    this.cleanup();

    document.addEventListener('htmx:responseError', this.onResponseError);
    document.addEventListener('htmx:timeout', this.onTimeout);
    document.addEventListener('keydown', this.onKeydown);
  }

  /**
   * Clean up existing event listeners and dialogs
   */
  cleanup() {
    const existingDialog = document.getElementById(this.dialogId);
    if (existingDialog) {
      existingDialog.remove();
    }

    document.removeEventListener('htmx:responseError', this.handleResponseError);
    document.removeEventListener('htmx:timeout', this.handleTimeout);
    document.removeEventListener('keydown', this.handleKeydown);
  }

  /**
   * Handle HTTP response errors (4xx, 5xx status codes)
   */
  handleResponseError(event) {
    const { xhr, target } = event.detail;
    const errorData = {
      type: 'Response Error',
      status: xhr.status,
      statusText: xhr.statusText,
      url:
        xhr.responseURL ||
        target?.getAttribute('hx-get') ||
        target?.getAttribute('hx-post') ||
        'Unknown',
      method: this.getRequestMethod(target, xhr),
      timestamp: new Date().toISOString(),
      triggerElement: this.getElementInfo(target),
      responseText: xhr.responseText || 'No response body',
      event: 'htmx:responseError',
    };

    console.log(
      `HTMX Error: ${errorData.type} - ${errorData.status} ${errorData.statusText}`,
      errorData
    );

    if (
      !event.detail.requestConfig.triggeringEvent.srcElement.attributes[
        'hx-custom-global-error-handling-disabled'
      ]
    ) {
      this.showErrorDialog(errorData);
    }

    if (window.Sentry) {
      window.Sentry.captureException(
        new Error(`HTMX Error: ${errorData.type} - ${errorData.status} ${errorData.statusText}`),
        {
          extra: {
            url: errorData.url,
            method: errorData.method,
            timestamp: errorData.timestamp,
            triggerElement: errorData.triggerElement,
            responseText: errorData.responseText,
          },
        }
      );
    }
  }

  /**
   * Handle request timeouts
   */
  handleTimeout(event) {
    const { xhr, target } = event.detail;
    const errorData = {
      type: 'Timeout Error',
      status: 0,
      statusText: 'Request Timeout',
      url: target?.getAttribute('hx-get') || target?.getAttribute('hx-post') || 'Unknown',
      method: this.getRequestMethod(target, xhr),
      timestamp: new Date().toISOString(),
      triggerElement: this.getElementInfo(target),
      responseText: 'Request timed out - the server took too long to respond',
      event: 'htmx:timeout',
    };

    this.showErrorDialog(errorData);
  }

  /**
   * Handle keyboard events (ESC to close dialog)
   */
  handleKeydown(event) {
    if (event.key === 'Escape' && this.isDialogOpen) {
      this.closeDialog();
    }
  }

  /**
   * Extract HTTP method from target element or XHR
   */
  getRequestMethod(target, xhr) {
    if (target?.getAttribute('hx-get')) return 'GET';
    if (target?.getAttribute('hx-post')) return 'POST';
    if (target?.getAttribute('hx-put')) return 'PUT';
    if (target?.getAttribute('hx-patch')) return 'PATCH';
    if (target?.getAttribute('hx-delete')) return 'DELETE';

    // Fallback to XHR method if available
    return xhr?.method || 'GET';
  }

  /**
   * Get information about the trigger element
   */
  getElementInfo(element) {
    if (!element) return 'Unknown element';

    const tagName = element.tagName.toLowerCase();
    const id = element.id ? `#${element.id}` : '';
    const classes = element.className ? `.${element.className.split(' ').join('.')}` : '';
    const text = element.textContent?.trim().substring(0, 50) || '';

    return `<${tagName}${id}${classes}>${text ? ` "${text}"` : ''}`;
  }

  /**
   * Create and show the error dialog
   */
  showErrorDialog(errorData) {
    this.currentErrorData = errorData;

    if (this.isDialogOpen) {
      this.closeDialog();
    }

    const dialog = this.createDialogElement(errorData);
    document.body.appendChild(dialog);

    this.createResponseIframe(dialog, errorData.responseText);

    dialog.showModal();
    this.isDialogOpen = true;

    this.setupDialogEventListeners(dialog);

    const closeButton = dialog.querySelector('.htmx-error-close-btn');
    if (closeButton) {
      closeButton.focus();
    }
  }

  /**
   * Create the dialog HTML element
   */
  createDialogElement(errorData) {
    const dialog = document.createElement('dialog');
    dialog.id = this.dialogId;
    dialog.className = 'htmx-error-dialog';

    dialog.innerHTML = `
      <div class="htmx-error-dialog-content">
        <header class="htmx-error-header">
          <div class="htmx-error-title">
            <h2>HTMX ${errorData.type}</h2>
            <div class="htmx-error-status">
              Status: ${errorData.status} ${errorData.statusText}
            </div>
          </div>
          <button type="button" class="htmx-error-close-btn" aria-label="Close dialog">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </header>
        
        <div class="htmx-error-body">
          <div class="htmx-error-metadata">
            <div class="htmx-error-meta-grid">
              <div class="htmx-error-meta-item">
                <strong>Timestamp:</strong>
                <span>${new Date(errorData.timestamp).toLocaleString()}</span>
              </div>
              <div class="htmx-error-meta-item">
                <strong>URL:</strong>
                <span class="htmx-error-url">${this.escapeHtml(errorData.url)}</span>
              </div>
              <div class="htmx-error-meta-item">
                <strong>Method:</strong>
                <span>${errorData.method}</span>
              </div>
              <div class="htmx-error-meta-item">
                <strong>Event:</strong>
                <span>${errorData.event}</span>
              </div>
              <div class="htmx-error-meta-item htmx-error-meta-trigger">
                <strong>Trigger Element:</strong>
                <code>${this.escapeHtml(errorData.triggerElement)}</code>
              </div>
            </div>
          </div>
          
          <div class="htmx-error-response-section">
            <h3>Server Response</h3>
            <div class="htmx-error-response-container">
              <div class="htmx-error-response-iframe-container"></div>
            </div>
          </div>
        </div>
        
        <footer class="htmx-error-footer">
          <button type="button" class="htmx-error-btn htmx-error-btn-secondary htmx-error-copy-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
            Copy Details
          </button>
          <button type="button" class="htmx-error-btn htmx-error-btn-secondary htmx-error-download-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7,10 12,15 17,10"></polyline>
              <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
            Download
          </button>
          <button type="button" class="htmx-error-btn htmx-error-btn-primary htmx-error-close-btn">
            Close
          </button>
        </footer>
      </div>
    `;

    return dialog;
  }

  /**
   * Create and insert the response iframe safely
   */
  createResponseIframe(dialog, responseText) {
    const container = dialog.querySelector('.htmx-error-response-iframe-container');
    if (!container) return;

    const iframe = document.createElement('iframe');
    iframe.className = 'htmx-error-response-iframe';
    iframe.setAttribute('sandbox', 'allow-same-origin');
    iframe.setAttribute('title', 'Error response content');

    // Set srcdoc directly to avoid template literal issues
    iframe.srcdoc = responseText;

    container.appendChild(iframe);
  }

  /**
   * Set up event listeners for dialog buttons
   */
  setupDialogEventListeners(dialog) {
    const closeButtons = dialog.querySelectorAll('.htmx-error-close-btn');
    closeButtons.forEach(btn => {
      btn.addEventListener('click', () => this.closeDialog());
    });

    const copyBtn = dialog.querySelector('.htmx-error-copy-btn');
    if (copyBtn) {
      copyBtn.addEventListener('click', () => this.copyErrorDetails());
    }

    const downloadBtn = dialog.querySelector('.htmx-error-download-btn');
    if (downloadBtn) {
      downloadBtn.addEventListener('click', () => this.downloadErrorDetails());
    }

    dialog.addEventListener('click', event => {
      if (event.target === dialog) {
        this.closeDialog();
      }
    });
  }

  /**
   * Close the error dialog
   */
  closeDialog() {
    const dialog = document.getElementById(this.dialogId);
    if (dialog) {
      dialog.close();
      dialog.remove();
    }
    this.isDialogOpen = false;
    this.currentErrorData = null;
  }

  /**
   * Copy error details to clipboard
   */
  async copyErrorDetails() {
    if (!this.currentErrorData) return;

    const textData = this.formatErrorDetailsAsText();

    try {
      await navigator.clipboard.writeText(textData);
      this.showCopyFeedback('Details copied to clipboard!');
    } catch {
      // Fallback for older browsers
      this.fallbackCopyToClipboard(textData);
    }
  }

  /**
   * Download error details as a text file
   */
  downloadErrorDetails() {
    if (!this.currentErrorData) return;

    const textData = this.formatErrorDetailsAsText();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `htmx-error-${timestamp}.txt`;

    const blob = new Blob([textData], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    this.showCopyFeedback('Error details downloaded!');
  }

  /**
   * Format error details as structured text for copying/downloading
   */
  formatErrorDetailsAsText() {
    const data = this.currentErrorData;

    return `HTMX ERROR REPORT
${'='.repeat(50)}

Error Type: ${data.type}
Status: ${data.status} ${data.statusText}
Timestamp: ${new Date(data.timestamp).toLocaleString()}
URL: ${data.url}
HTTP Method: ${data.method}
Event: ${data.event}

Trigger Element:
${data.triggerElement}

${'='.repeat(50)}
SERVER RESPONSE
${'='.repeat(50)}
${data.responseText}

${'='.repeat(50)}
END OF REPORT
${'='.repeat(50)}
`;
  }

  /**
   * Format headers as text
   */
  formatHeadersAsText(headers) {
    const entries = Object.entries(headers);
    if (entries.length === 0) {
      return 'No headers available';
    }

    return entries.map(([key, value]) => `${key}: ${value}`).join('\n');
  }

  /**
   * Show copy feedback message
   */
  showCopyFeedback(message) {
    const copyBtn = document.querySelector('.htmx-error-copy-btn');
    if (!copyBtn) return;

    const originalText = copyBtn.innerHTML;
    copyBtn.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="20,6 9,17 4,12"></polyline>
      </svg>
      ${message}
    `;

    setTimeout(() => {
      copyBtn.innerHTML = originalText;
    }, 2000);
  }

  /**
   * Fallback copy method for older browsers
   */
  fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.select();

    try {
      document.execCommand('copy');
      this.showCopyFeedback('Details copied to clipboard!');
    } catch {
      this.showCopyFeedback('Copy failed - please select and copy manually');
    }

    document.body.removeChild(textArea);
  }

  /**
   * Escape HTML to prevent XSS
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;

    return div.innerHTML;
  }

  /**
   * Destroy the error handler and clean up resources
   */
  destroy() {
    this.cleanup();
    this.currentErrorData = null;
  }
}
