import { HTMXConfig } from '/js/htmx-config.js';
import { Toast } from '/js/toast.js';

export class App {
  constructor() {
    this.htmxConfig = new HTMXConfig(false);
    this.keyboardShortcuts = new KeyboardShortcuts();
    this.toast = new Toast();

    this.setupDarkModeToggle();
    document.addEventListener('click', event => this.autoDisableButtons(event));
  }

  setupDarkModeToggle() {
    const themeToggleDarkIcon = document.querySelector('#theme-toggle-dark-icon');
    const themeToggleLightIcon = document.querySelector('#theme-toggle-light-icon');
    const themeToggleBtn = document.querySelector('#theme-toggle');

    // Helper function to apply theme changes
    const applyTheme = isDark => {
      if (isDark) {
        document.documentElement.classList.add('dark');
        if (themeToggleLightIcon) themeToggleLightIcon.classList.remove('hidden');
        if (themeToggleDarkIcon) themeToggleDarkIcon.classList.add('hidden');
        localStorage.setItem('color-theme', 'dark');
      } else {
        document.documentElement.classList.remove('dark');
        if (themeToggleDarkIcon) themeToggleDarkIcon.classList.remove('hidden');
        if (themeToggleLightIcon) themeToggleLightIcon.classList.add('hidden');
        localStorage.setItem('color-theme', 'light');
      }
    };

    let initialIsDark = false;
    const storedTheme = localStorage.getItem('color-theme');

    if (storedTheme) {
      initialIsDark = storedTheme === 'dark';
    } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      initialIsDark = true;
    }

    applyTheme(initialIsDark);

    if (themeToggleBtn) {
      themeToggleBtn.addEventListener('click', () => {
        const currentlyDark = document.documentElement.classList.contains('dark');
        applyTheme(!currentlyDark); // Toggle the theme
      });
    }
  }

  autoDisableButtons(event) {
    const target = event.target.closest('button[data-auto-disable]:not([disabled])');
    if (!target) return;

    if (target.dataset.noDisable) return;

    target.disabled = true;
    target.classList.remove('hover:cursor-pointer');
    target.classList.add('opacity-70');
  }
}
