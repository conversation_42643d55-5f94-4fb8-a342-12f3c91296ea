import { HTMXErrorHandler } from '/js/htmx-error-handler.js';

export class HTMXConfig {
  constructor(debug = false) {
    this.setupDebugHandler(debug);
    this.setupDOMEventHandlers();
    this.errorHandler = new HTMXErrorHandler();
  }

  setupDebugHandler() {
    document.addEventListener('htmx:beforeOnLoad', event => {
      if (!window.location.search.includes('htmxdebug')) return;
      const xhr = event.detail.xhr;
      if (xhr.status === 500 || xhr.status === 404) {
        event.stopPropagation();
        document.children[0].innerHTML = xhr.response;
        // (1, eval) wtf - see https://stackoverflow.com/questions/9107240/1-evalthis-vs-evalthis-in-javascript
        /* eslint-disable no-eval */
        (1, eval)(document.scripts[0].innerText);
        /* eslint-enable no-eval */
        window.onload();
      }
    });
  }

  setupDOMEventHandlers() {
    document.addEventListener('DOMContentLoaded', () => {
      this.setCsrfToken();
    });
  }

  setCsrfToken() {
    const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
    document.body.setAttribute('hx-headers', `{"x-csrftoken": "${csrfToken}"}`);
  }
}
