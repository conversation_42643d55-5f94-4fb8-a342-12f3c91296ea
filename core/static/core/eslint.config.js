export default [
  {
    ignores: ['vendor', '**/node_modules/**', '**/dist/**', '**/build/**'],
  },
  {
    // Global settings
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
      globals: {
        document: 'readonly',
        localStorage: 'readonly',
        navigator: 'readonly',
        window: 'readonly',
        $: 'readonly',
        fetch: 'readonly',
        console: 'readonly',
        URLSearchParams: 'readonly',
        setTimeout: 'readonly',
        clearTimeout: 'readonly',
        Blob: 'readonly',
        URL: 'readonly',
      },
    },
    linterOptions: {
      reportUnusedDisableDirectives: true,
    },
    // Base rules
    rules: {
      // Error prevention
      'no-debugger': ['error'],
      'no-unused-vars': ['warn'],
      'no-undef': ['error'],
      'no-dupe-args': ['error'],
      'no-dupe-keys': ['error'],
      'no-duplicate-case': ['error'],
      'no-irregular-whitespace': ['error'],

      // Best practices
      'array-callback-return': ['error'],
      'block-scoped-var': ['error'],
      curly: ['error', 'multi-line'],
      'default-case': ['warn'],
      'dot-notation': ['warn'],
      eqeqeq: ['error', 'always', { null: 'ignore' }],
      'no-eval': ['error'],
      'no-floating-decimal': ['error'],
      'no-implied-eval': ['error'],
      'no-multi-spaces': ['error'],
      'no-useless-return': ['error'],

      // ES6 features
      'arrow-spacing': ['error', { before: true, after: true }],
      'no-duplicate-imports': ['error'],
      'no-var': ['error'],
      'prefer-const': ['warn'],
      'prefer-template': ['warn'],

      // Stylistic (compatible with Prettier)
      'padding-line-between-statements': [
        'error',
        { blankLine: 'always', prev: '*', next: 'return' },
      ],
    },
  },
];
