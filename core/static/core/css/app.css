.number-input-clean {
  -webkit-appearance: none;
  -moz-appearance: textfield;
}

.number-input-clean::-webkit-outer-spin-button,
.number-input-clean::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.htmx-indicator {
  transition: opacity 50ms ease-in;
}

.htmx-indicator.indicator-display-block {
  display: none;
}

body .htmx-request .htmx-indicator.indicator-display-block {
  display: block;
}

body .htmx-request.htmx-indicator.indicator-display-block {
  display: block;
}

/* Opacity version */
.htmx-indicator.indicator-opacity {
  opacity: 0;
  pointer-events: none;
}

body .htmx-request .htmx-indicator.indicator-opacity {
  opacity: 1;
  pointer-events: auto;
}

body .htmx-request.htmx-indicator.indicator-opacity {
  opacity: 1;
  pointer-events: auto;
}
