/**
 * HTMX Erro<PERSON> Styles
 * 
 * High-contrast, professional styling for HTMX error dialogs.
 * Optimized for screenshots and bug reports.
 * Responsive design with dark mode support.
 */

/* Dialog backdrop and positioning */
.htmx-error-dialog {
  /* Reset default dialog styles */
  border: none;
  padding: 0;
  margin: 0;

  /* Positioning and sizing */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;

  /* Size constraints */
  width: min(95vw, 900px);
  max-height: 90vh;

  /* Visual styling */
  background: white;
  border-radius: 8px;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(0, 0, 0, 0.05);

  /* Smooth animations */
  transition: all 0.2s ease-out;

  /* Ensure proper layering */
  backdrop-filter: blur(4px);
}

.htmx-error-dialog::backdrop {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .htmx-error-dialog {
    background: #1f2937;
    box-shadow:
      0 25px 50px -12px rgba(0, 0, 0, 0.5),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }
}

.dark .htmx-error-dialog {
  background: #1f2937;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Dialog content container */
.htmx-error-dialog-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 90vh;
  overflow: hidden;
}

/* Header section */
.htmx-error-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24px 24px 16px 24px;
  border-bottom: 2px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 8px 8px 0 0;
}

.dark .htmx-error-header {
  border-bottom-color: #374151;
  background: #111827;
}

.htmx-error-title h2 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #dc2626;
  line-height: 1.2;
}

.dark .htmx-error-title h2 {
  color: #ef4444;
}

.htmx-error-status {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
  font-family: 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', monospace;
}

.dark .htmx-error-status {
  color: #9ca3af;
}

.htmx-error-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: #6b7280;
  transition: all 0.15s ease;
  flex-shrink: 0;
}

.htmx-error-close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.dark .htmx-error-close-btn {
  color: #9ca3af;
}

.dark .htmx-error-close-btn:hover {
  background: #374151;
  color: #f3f4f6;
}

/* Body section */
.htmx-error-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  color: #111827;
}

.dark .htmx-error-body {
  color: #f9fafb;
}

/* Metadata grid */
.htmx-error-metadata {
  margin-bottom: 32px;
}

.htmx-error-meta-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.htmx-error-meta-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.htmx-error-meta-item strong {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.dark .htmx-error-meta-item strong {
  color: #d1d5db;
}

.htmx-error-meta-item span,
.htmx-error-meta-item code {
  font-size: 0.875rem;
  word-break: break-all;
  padding: 8px 12px;
  background: #f3f4f6;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.htmx-error-meta-item code {
  font-family: 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', monospace;
  font-weight: 500;
}

.dark .htmx-error-meta-item span,
.dark .htmx-error-meta-item code {
  background: #374151;
  border-color: #4b5563;
  color: #f3f4f6;
}

.htmx-error-url {
  font-family: 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', monospace;
  font-weight: 500;
}

.htmx-error-meta-trigger {
  grid-column: 1 / -1;
}

/* Response section */
.htmx-error-response-section {
  margin-bottom: 32px;
}

.htmx-error-response-section h3,
.htmx-error-headers-section h3 {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.dark .htmx-error-response-section h3,
.dark .htmx-error-headers-section h3 {
  color: #d1d5db;
  border-bottom-color: #4b5563;
}

.htmx-error-response-container {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background: #ffffff;
}

.dark .htmx-error-response-container {
  border-color: #4b5563;
  background: #1f2937;
}

.htmx-error-response-iframe {
  width: 100%;
  height: 300px;
  border: none;
  background: white;
}

/* Headers sections */
.htmx-error-headers-section {
  margin-bottom: 24px;
}

.htmx-error-headers-list {
  display: grid;
  gap: 8px;
}

.htmx-error-header-item {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 16px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  align-items: start;
}

.dark .htmx-error-header-item {
  background: #374151;
  border-color: #4b5563;
}

.htmx-error-header-item strong {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
  font-family: 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', monospace;
}

.dark .htmx-error-header-item strong {
  color: #9ca3af;
}

.htmx-error-header-item span {
  font-size: 0.875rem;
  font-family: 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', monospace;
  word-break: break-all;
  color: #111827;
}

.dark .htmx-error-header-item span {
  color: #f3f4f6;
}

.htmx-error-no-headers {
  padding: 16px;
  text-align: center;
  color: #6b7280;
  font-style: italic;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px dashed #d1d5db;
}

.dark .htmx-error-no-headers {
  color: #9ca3af;
  background: #374151;
  border-color: #6b7280;
}

/* Footer section */
.htmx-error-footer {
  display: flex;
  gap: 12px;
  padding: 16px 24px 24px 24px;
  border-top: 2px solid #e5e7eb;
  background: #f9fafb;
  justify-content: flex-end;
  border-radius: 0 0 8px 8px;
}

.dark .htmx-error-footer {
  border-top-color: #374151;
  background: #111827;
}

/* Button styles */
.htmx-error-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  border: 1px solid transparent;
  text-decoration: none;
  min-width: 120px;
  justify-content: center;
}

.htmx-error-btn-primary {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.htmx-error-btn-primary:hover {
  background: #1d4ed8;
  border-color: #1d4ed8;
}

.htmx-error-btn-secondary {
  background: white;
  color: #374151;
  border-color: #d1d5db;
}

.htmx-error-btn-secondary:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.dark .htmx-error-btn-secondary {
  background: #4b5563;
  color: #f3f4f6;
  border-color: #6b7280;
}

.dark .htmx-error-btn-secondary:hover {
  background: #374151;
  border-color: #9ca3af;
}

/* Responsive design */
@media (max-width: 640px) {
  .htmx-error-dialog {
    width: 95vw;
    height: 95vh;
    max-height: 95vh;
    margin: 0;
    transform: none;
    top: 2.5vh;
    left: 2.5vw;
  }

  .htmx-error-header,
  .htmx-error-body,
  .htmx-error-footer {
    padding-left: 16px;
    padding-right: 16px;
  }

  .htmx-error-meta-grid {
    grid-template-columns: 1fr;
  }

  .htmx-error-header-item {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .htmx-error-footer {
    flex-direction: column;
  }

  .htmx-error-btn {
    width: 100%;
  }

  .htmx-error-response-iframe {
    height: 200px;
  }
}

@media (max-height: 600px) {
  .htmx-error-response-iframe {
    height: 150px;
  }
}

/* Focus and accessibility styles */
.htmx-error-btn:focus,
.htmx-error-close-btn:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

.dark .htmx-error-btn:focus,
.dark .htmx-error-close-btn:focus {
  outline-color: #60a5fa;
}

/* Animation for dialog entrance */
.htmx-error-dialog[open] {
  animation: htmx-error-dialog-show 0.2s ease-out;
}

@keyframes htmx-error-dialog-show {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .htmx-error-dialog {
    border: 3px solid;
  }

  .htmx-error-header,
  .htmx-error-footer {
    border-width: 3px;
  }

  .htmx-error-btn {
    border-width: 2px;
  }

  .htmx-error-response-container {
    border-width: 3px;
  }
}

/* Print styles for documentation */
@media print {
  .htmx-error-dialog {
    position: static;
    transform: none;
    width: 100%;
    max-height: none;
    box-shadow: none;
    border: 2px solid #000;
  }

  .htmx-error-footer {
    display: none;
  }

  .htmx-error-response-iframe {
    border: 1px solid #000;
    height: 400px;
  }
}
