from typing import TYPE_CHECKING

from django.contrib.auth import get_user_model
from django.db import models
from django.urls import reverse

if TYPE_CHECKING:
    pass

User = get_user_model()


class BaseModel(models.Model):
    id: models.AutoField
    objects: models.Manager["BaseModel"]

    class Meta:
        abstract = True


class ActivityLogType(models.TextChoices):
    INFO = "INFO", "Info"
    WARNING = "WARNING", "Warning"
    ERROR = "ERROR", "Error"
    DEBUG = "DEBUG", "Debug"


class ActivityLog(BaseModel):
    text = models.TextField(blank=True, null=True)
    type = models.CharField(max_length=10, choices=ActivityLogType.choices, default=ActivityLogType.INFO)
    data = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Type annotations for Django's automatic attributes
    objects: models.Manager["ActivityLog"]

    class Meta:
        abstract = False
        ordering = ("-created_at",)

    def __str__(self):
        return self.summary

    @property
    def summary(self):
        text = self.text or ""
        return f"[{self.type}]: {text[:100]}{'...' if len(text) > 100 else ''}"


class ErrorType(models.TextChoices):
    NOT_FOUND = "404", "Not Found"
    SERVER_ERROR = "500", "Server Error"
    OTHER = "OTHER", "Other"


class ErrorReport(BaseModel):
    """Model for storing user-submitted error reports"""

    error_type = models.CharField(max_length=10, choices=ErrorType.choices, default=ErrorType.SERVER_ERROR)
    url = models.URLField()
    user = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    traceback = models.TextField(blank=True, null=True)
    context = models.JSONField(blank=True, default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Error Report"
        verbose_name_plural = "Error Reports"
        ordering = ("-created_at",)

        constraints = [
            models.CheckConstraint(
                name="%(app_label)s_%(class)s_error_type_valid",
                check=models.Q(error_type__in=ErrorType.values),
            )
        ]

    def __str__(self):
        return f"Error {self.error_type} at {self.url} ({self.created_at})"

    def get_absolute_url(self):
        return reverse("admin:core_errorreport_change", args=[self.id])
