import inspect
import os
import subprocess
from functools import wraps
from typing import Any, Dict, Optional, Type, TypeVar

from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Model
from django.http import Http404, HttpResponseForbidden

# Generic type variable for model types
ModelType = TypeVar("ModelType", bound=Model)


def strtobool(value: str) -> bool:
    return value.lower() in ("y", "yes", "t", "true", "on", "1")


def is_path_absolute(path):
    return path.startswith("/") or path.startswith("http")


def app_version():
    # Get version from environment variable set in Dockerfile
    # This will already include git branch and commit if using 'latest' tag
    version = os.environ.get("VERSION", "latest")

    # Only try to add git info if it's not already included in the version
    if "-" not in version:
        try:
            branch = subprocess.check_output(["git", "rev-parse", "--abbrev-ref", "HEAD"], stderr=subprocess.DEVNULL).strip().decode("utf-8")
            commit = subprocess.check_output(["git", "rev-parse", "--short", "HEAD"], stderr=subprocess.DEVNULL).strip().decode("utf-8")
            version += f" ({branch}-{commit})"
        except (FileNotFoundError, subprocess.CalledProcessError):
            pass

    return version


class DetailedHttp404(Http404):
    """Custom Http404 exception that includes details about the model and lookup parameters"""

    def __init__(self, model: Type[Model], params: Dict[str, Any], msg: Optional[str] = None):
        self.model = model
        self.params = params
        self.model_name = model.__name__
        message = msg or f"No {model.__name__} found matching the query: {params}"
        super().__init__(message)

    def __str__(self):
        return f"No {self.model_name} found matching the query: {self.params}"


def get_object_or_404(model: Type[ModelType], *args, **kwargs) -> ModelType:
    """
    Custom version of Django's get_object_or_404 that captures model and lookup parameters
    for better error reporting.

    Args:
        model: The model class to query
        *args: Positional arguments to pass to the model's get() method
        **kwargs: Keyword arguments to pass to the model's get() method

    Returns:
        The found model instance

    Raises:
        DetailedHttp404: If the object is not found, with details about the model and parameters
    """
    try:
        return model.objects.get(*args, **kwargs)
    except ObjectDoesNotExist as exc:
        # Get the calling function's name and arguments for better context
        frame = inspect.currentframe()
        if frame and frame.f_back:
            func_name = frame.f_back.f_code.co_name
        else:
            func_name = "unknown"

        # Add the calling function to the params for context
        params = kwargs.copy()
        params["_caller"] = func_name

        raise DetailedHttp404(model=model, params=params) from exc


def internal_network_required(view_func):
    """
    Decorator that only allows requests from internal network IPs (e.g., Docker network).
    Used for securing endpoints that perform system operations without requiring authentication.
    """

    @wraps(view_func)
    def wrapped_view(request, *args, **kwargs):

        if request.headers.get("X-Forwarded-For"):
            return HttpResponseForbidden()

        return view_func(request, *args, **kwargs)

    return wrapped_view


def is_admin(user) -> bool:
    return user.is_superuser or user.groups.filter(name="Admins").exists()
