from random import random

from django.conf import settings

from core.utils import app_version

VERSION = app_version()


def core(_):
    return {
        "version": VERSION,
        "environment": settings.ENV,
        "pronto_environment": settings.PRONTO_ENVIRONMENT,
        "cachebust": f"?v={round(random() * 1000000)}" if settings.DEBUG else "",
        "sentry_enabled": settings.SENTRY_ENABLED,
    }
