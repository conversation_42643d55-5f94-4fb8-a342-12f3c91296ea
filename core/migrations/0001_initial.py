# Generated by Django 5.2.1 on 2025-06-12 07:08

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ActivityLog",
            fields=[
                ("id", models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("text", models.TextField(blank=True, null=True)),
                (
                    "type",
                    models.CharField(
                        choices=[("INFO", "Info"), ("WARNING", "Warning"), ("ERROR", "Error"), ("DEBUG", "Debug")], default="INFO", max_length=10
                    ),
                ),
                ("data", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ("-created_at",),
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="ErrorReport",
            fields=[
                ("id", models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "error_type",
                    models.CharField(choices=[("404", "Not Found"), ("500", "Server Error"), ("OTHER", "Other")], default="500", max_length=10),
                ),
                ("url", models.URLField()),
                ("description", models.TextField(blank=True, null=True)),
                ("traceback", models.TextField(blank=True, null=True)),
                ("context", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("user", models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                "verbose_name": "Error Report",
                "verbose_name_plural": "Error Reports",
                "ordering": ("-created_at",),
                "constraints": [
                    models.CheckConstraint(condition=models.Q(("error_type__in", ["404", "500", "OTHER"])), name="core_errorreport_error_type_valid")
                ],
            },
        ),
    ]
