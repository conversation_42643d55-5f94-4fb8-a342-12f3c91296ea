from django.contrib import admin

from .models import ActivityLog, ErrorReport


@admin.register(ActivityLog)
class ActivityLogAdmin(admin.ModelAdmin):
    list_display = ("text", "created_at", "data")
    list_filter = ("created_at",)
    search_fields = ("text", "action", "data")


@admin.register(ErrorReport)
class ErrorReportAdmin(admin.ModelAdmin):
    list_display = ("error_type", "url", "user", "created_at")
    list_filter = ("error_type", "created_at")
    search_fields = ("url", "description", "user__username")
    readonly_fields = ("error_type", "url", "user", "description", "created_at", "updated_at", "traceback", "context")
