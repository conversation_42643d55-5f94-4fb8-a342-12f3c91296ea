from django import template
from django.templatetags.static import static
from django.utils.safestring import mark_safe

register = template.Library()


@register.simple_tag
def import_map():
    """
    Renders the import map for JavaScript modules.
    This allows consistent module imports across the application.
    """
    js_files = {
        "/js/admin.js": static("core/js/admin.js"),
        "/js/app.js": static("core/js/app.js"),
        "/js/htmx-config.js": static("core/js/htmx-config.js"),
        "/js/htmx-error-handler.js": static("core/js/htmx-error-handler.js"),
        "/js/table.js": static("core/js/table.js"),
        "/js/toast.js": static("core/js/toast.js"),
    }

    import_map_json = "{\n"
    import_map_json += '    "imports": {\n'

    for i, (key, value) in enumerate(js_files.items()):
        import_map_json += f'        "{key}": "{value}"'
        if i < len(js_files) - 1:
            import_map_json += ","
        import_map_json += "\n"

    import_map_json += "    }\n"
    import_map_json += "}"

    return mark_safe(f'<script type="importmap">\n{import_map_json}\n</script>')
