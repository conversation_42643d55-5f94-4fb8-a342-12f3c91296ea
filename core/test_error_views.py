"""
Test views for error logging and HTMX error handler testing.
"""

import json
import time

from django.http import Http404, HttpResponse
from django.views.decorators.csrf import csrf_exempt


@csrf_exempt
def error_404(request):
    """Trigger a 404 error for testing."""
    raise Http404("This is a test 404 error for HTMX error handler testing.")


@csrf_exempt
def error_500(request):
    """Trigger a 500 error for testing."""
    0 / 0


@csrf_exempt
def error_403(request):
    """Trigger a 403 error for testing."""
    from django.http import HttpResponseForbidden

    return HttpResponseForbidden("<h1>403 Forbidden</h1><p>This is a test 403 error with HTML content for HTMX error handler testing.</p>")


@csrf_exempt
def timeout_error(request):
    """Trigger a timeout by sleeping longer than expected."""
    time.sleep(10)  # Sleep for 10 seconds to trigger timeout
    return HttpResponse("This response should not be reached due to timeout.")


@csrf_exempt
def complex_error(request):
    """Test complex error with detailed response."""
    error_response = {
        "error": "Complex test error",
        "details": "This is a detailed error response with JSON data",
        "timestamp": "2024-01-01T12:00:00Z",
        "request_data": {
            "method": request.method,
            "path": request.path,
            "headers": dict(request.headers),
        },
        "validation_errors": [
            {"field": "name", "message": "Name is required"},
            {"field": "email", "message": "Invalid email format"},
        ],
    }

    response = HttpResponse(json.dumps(error_response, indent=2), content_type="application/json", status=422)
    response.reason_phrase = "Unprocessable Entity"
    return response


@csrf_exempt
def html_error(request):
    """Test error with rich HTML content."""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Error Page</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .error-container { 
                background: #fee2e2; 
                border: 2px solid #dc2626; 
                padding: 20px; 
                border-radius: 8px; 
            }
            .error-title { color: #dc2626; font-size: 24px; margin-bottom: 10px; }
            .error-details { background: #fff; padding: 15px; border-radius: 4px; }
            code { background: #f3f4f6; padding: 2px 4px; border-radius: 3px; }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-title">Test HTML Error Response</div>
            <p>This is a rich HTML error response to test how the HTMX error handler displays HTML content.</p>
            
            <div class="error-details">
                <h3>Error Details:</h3>
                <ul>
                    <li><strong>Status:</strong> 400 Bad Request</li>
                    <li><strong>Type:</strong> Validation Error</li>
                    <li><strong>Timestamp:</strong> 2024-01-01 12:00:00 UTC</li>
                </ul>
                
                <h3>Request Information:</h3>
                <p>Method: <code>GET</code></p>
                <p>Path: <code>/test-error/html</code></p>
                
                <h3>Troubleshooting:</h3>
                <ol>
                    <li>Check your request parameters</li>
                    <li>Verify authentication credentials</li>
                    <li>Contact support if the problem persists</li>
                </ol>
            </div>
        </div>
    </body>
    </html>
    """

    return HttpResponse(html_content, status=400)
