# Input / Output

## Inputs

### Profile Inputs

- Profile: profileentry
- Measure: measureentry
- OD (<= 600): ODentry
- ID >= 5: IDentry
- CH: CHentry
- Extra: extraentry
- Material1: MATL1entry
- Material2: MATL2entry
- Material3: MATL3entry

# Profile Outputs

- Profile: D6
- Measure: D7
- OD: D8
- ID: D9
- CH: D10
- Attribute1: CH11
- Attribute2: CH12
- Attribute3: C13
- Attribute4: C14

## Results

#### Part
- Part number:
- Part description => partdesc

### Prices

- Range B17-B21
  Key: B
  Value C

## Manufacturing

Range B24 - 26
B: Materialn
C: Billet required: 
D: Min Manufacturing time
E: Billet Material required

## Error handling

If Cost Price == 0, get reaason from partdesc

If Range("partdesc") = "Material/Size not available" Then
    MsgBox ("Material/Size not available. Part not added to Entry History")
    GoTo endofmassentry
End If

