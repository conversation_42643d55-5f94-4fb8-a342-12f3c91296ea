# Input / Output

## Inputs

- Profile: profileentry # List[str] from a static list
- Measure: measureentry # List[str] from a static list
- OD (<= 600): ODentry # float to 3 decimal places
- ID >= 5: IDentry # float to 3 decimal places
- CH: CHentry # float to 3 decimal places
- Extra: extraentry # Optional[int], depends upon whether the excel macro a key in the previous cell
- Material1: MATL1entry # Optional[List[str]], optional depending upon whether the excel macro populates a list of values
- Material2: MATL2entry # Optional[List[str]], optional depending upon whether the excel macro populates a list of values
- Material3: MATL3entry # Optional[List[str]], optional depending upon whether the excel macro populates a list of values



## Profile Outputs

## profile_inputs

Note: These are the inputs passed in the API request so we can marry them up to the outputs

{
  "profile": "",
  "measure": "",
  "od": "",
  "id": "",
  "ch": "",
  "extra": "",
  "material1": "",
  "material2": "",
  "material3": ""
}

### profile_outputs

- profile: profileentry # str
- hallite_ref: C7 # str
- measure1: # dict
  - measure: measureentry# str
  - od: ODentry # decimal
  - id: IDentry # decimal
  - ch: CHentry # decimal
  - extra: dict
    - key: extratype string
    - value: extraentry # int
  - material1: MATL1entry # ≠str
  - material2: MATL2entry # str
  - material3: MATL3entry # str
- measure2:
  - measure: measureentry # str
  - od: D8 # decimal
  - id: D9 # decimal
  - ch: D10 # decimal
  - extra:
    - key: extratype # str
    - value: D11 # int
  - material1: D12 # str
  - material2: D13 # str
  - material3: D14 # str

### part
- part_number:  C31 # str
- part_description: partdesc # str

### price

- cost_price: costpriceevco # currency
- abst: C19 # currency 
- b1: C20 # currency
- retail:: C21 # currency

### materials

- material1
  - billet_required: billetreqd1 # str
  - min_manufacturing_time: mctime1 # decimal
  - billet_material_required: mmreqd1 # str
- material2
  - billet_required: billetreqd2
  - min_manufacturing_time: mctime2
  - billet_material_required: mmreqd2
- material3
  - billet_required: billetreqd3
  - min_manufacturing_time: mctime3
  - billet_material_required: mmreqd3

## Error handling

If Cost Price == 0, get reaason from partdesc

If Range("partdesc") = "Material/Size not available" Then
    MsgBox ("Material/Size not available. Part not added to Entry History")
    GoTo endofmassentry
End If

If partdesc == "ID too small. Check with Technical."

if partdescr == "OD TOO LARGE"

if partdesc == Material/Size not available