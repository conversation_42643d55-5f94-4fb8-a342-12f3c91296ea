# Form Behaviour

- Profile => List[str] from a static list
- Measure: string Metric|Imperial
- OD: float to 3 decimal places
- ID: float to 3 decimal places
- CH: float to 3 decimal places
- Extra: Optional[int], depends upon whether the excel macro a key in the previous cell
- Material1: Optional[List[str]], optional depending upon whether the excel macro populates a list of values
- Material2: List[str], optional depending upon whether the excel macro populates a list of values
- Material3: List[str], optional depending upon whether the excel macro populates a list of values

