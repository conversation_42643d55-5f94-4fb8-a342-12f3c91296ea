[project]
name = "hallite-configurator"
version = "0.0.0"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
  "django>=5.2.1",
  "django-admin-interface>=0.30.0",
  "django-dynamic-raw-id>=4.4",
  "django-htmx>=1.23.0",
  "psutil>=6.1.1",
  "whitenoise>=6.9.0",
  "gunicorn>=23.0.0",
  "raven>=6.10.0",
  "sentry-sdk[django]>=2.29.1",
  "django-extensions>=4.1",
  "django-click>=2.4.1",
  "django-tailwind-cli[django-extensions]==4.1.0",
  "rust-just>=1.4.0",
  "django-components>=0.139.1",
  "pillow>=11.2.1",
  "logfire[django,sqlite3]>=3.16.0",
  "pyopenssl>=25.1.0",
  "highlight-io>=0.10.2",
  "django-tailwind[reload]>=4.0.1",
  "django-browser-reload>=1.18.0",
  "pyyaml>=6.0.2",
  "xlwings>=0.33.15",
]

[dependency-groups]
dev = [
  "Werkzeug[watchdog]",
  "ipdb",
  "flake8",
  "flake8-isort",
  "black",
  "pylint-django",
  "doppler-env",
  "django-debug-toolbar",
  "pre-commit",
  "ipython",
  "djlint",
  "pip-tools",
  "debugpy",
  "pylint-django",
  "prospector",
  "djhtml",
  "autoflake",
  "ruff",
  "ty",
  "django-types",
]

test = [
  "pytest-django",
  "pytest-cov",
  "coverage"
]

[tool.setuptools]
packages = ["core"]

# ==== pytest ====
[tool.pytest.ini_options]
addopts = "-xvs --color=yes --nomigrations --reuse-db"
norecursedirs = ".git* frontend media static templates .venv node_modules"
python_files = ["test_*.py"]
testpaths = ["tests"]
python_classes = ["Test*"]
python_functions = ["test_*"]
DJANGO_SETTINGS_MODULE = "config.settings"
django_find_project = true
# Exclude specific files from being collected as tests
filterwarnings = ["ignore::DeprecationWarning"]
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)8s] %(name)s: %(message)s"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"

[tool.black]
line-length = 150
target-version = ['py313']
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | build
  | hallite_configurator.egg-info
  | buck-out
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
line_length = 150
known_first_party = ["config"]
skip = [".venv/"]
skip_glob = ["**/migrations/*.py"]

[tool.pylint.MASTER]
ignore = '.git,.venv,node_modules,*/migrations/*'
load-plugins = ["pylint_django"]
django-settings-module = "config.settings"

[tool.pylint.FORMAT]
max-line-length = 150

[tool.pylint."MESSAGES CONTROL"]
disable = ["missing-docstring", "invalid-name", "import-outside-toplevel", "too-few-public-methods", "unspecified-encoding", "too-many-locals", "redefined-builtin", "line-too-long"]

[tool.pylint.DESIGN]
max-parents = 13

[tool.pylint.TYPECHECK]
generated-members = [
  "REQUEST",
  "acl_users",
  "aq_parent",
  "[a-zA-Z]+_set{1,2}",
  "save",
  "delete",
]

[tool.coverage.run]
source = ["core"]
omit = [
  "*/admin.py",
  "*/manage.py",
  "*/migrations/*",
  "*/tests/*",
  "conftest.py",
]

[tool.coverage.report]
exclude_lines = [
  "pragma: no cover",
  "def __repr__",
  "raise NotImplementedError",
  'if __name__ == "__main__"',
  "pass",
  "raise ImportError",
]

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
  ".bzr",
  ".direnv",
  ".eggs",
  ".git",
  ".github",
  ".hg",
  ".ruff_cache",
  ".svn",
  ".tox",
  ".venv",
  "__pypackages__",
  "_build",
  "build",
  "dist",
  "migrations",
  "node_modules",
  "static",
]
# Same as Black.
line-length = 150
# Assume Python 3.12.
target-version = "py313"

[tool.ruff.lint]
# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"
# Allow autofix for all enabled rules (when `--fix`) is provided.
fixable = ["A", "B", "C", "D", "E", "F"]
ignore = ["E501", "E741"]                # temporary
per-file-ignores = {}
# Enable Pyflakes `E` and `F` codes by default.
select = ["E", "F"]
unfixable = []

[tool.djlint]
# H017: Void tags should be self closing.
# H031:	Consider adding meta keywords.
ignore = "H017,H031"
preserve_blank_lines = true
indent = 2

# ==== autoflake ====
[tool.autoflake]
remove-all-unused-imports = true
remove-unused-variables = true
remove-duplicate-keys = true
ignore-init-module-imports = true
expand-star-imports = true
exclude = ["*/migrations/*", ".venv", "node_modules"]
