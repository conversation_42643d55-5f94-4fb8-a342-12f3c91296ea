import os

import xlwings as xw


class ExcelConfigurationError(Exception):
    """Raised when Excel is not properly configured for macro execution."""

    pass


# TODO

# [x] Access file ./assets/E-quote v7.0.xlsm (visible=True)
# [x] Set value in cell: profileentry = HK117
# [ ] Click button or execute macro (macro = runme)
# [ ] Get list of values from MATL1entry
# [ ] Set dropdown value: MATL1entry = EPDM


def access_excel_file(visible=True):
    """Access the E-quote Excel file and return the workbook object."""
    # Get the path to the Excel file
    file_path = os.path.join("assets", "E-quote v7.0.xlsm")

    # Check if file exists
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Excel file not found: {file_path}")

    # Create or get Excel app instance with visibility control
    app = xw.App(visible=visible, add_book=False)

    # Disable all alerts and warnings BEFORE opening the file
    app.display_alerts = False
    app.enable_events = False

    # Try to suppress macro security warnings
    # Note: These may not work if Trust Center settings require user interaction
    try:
        app.api.AutomationSecurity = 1  # msoAutomationSecurityLow
    except:
        pass  # This might fail on some Excel versions

    # Optional: Disable screen updating for better performance
    # app.screen_updating = False

    # Open the workbook (this is a .xlsm file with macros)
    wb = app.books.open(file_path)
    print(f"Successfully opened: {file_path}")
    print(f"Excel visible: {app.visible}")

    # Check macros immediately after opening
    check_macros_enabled(wb)

    return wb


def check_macros_enabled(workbook):
    """Check if macros are enabled in the workbook."""
    try:
        # Try to access VBA project - this will fail if macros are disabled
        vba_project = workbook.api.VBProject
        # If we can access it, try to get the project name to confirm it's really accessible
        project_name = vba_project.Name
        print(f"✅ Macros are enabled and accessible (VBA Project: {project_name})")
        return True
    except Exception as e:
        # Check if this is specifically a macro security issue
        error_msg = str(e).lower()
        if "programmatic access" in error_msg or "macro" in error_msg or "vbproject" in error_msg:
            raise ExcelConfigurationError(f"Macros are not enabled or programmatic access is disabled. " f"Original error: {e}") from e
        else:
            # Some other error, re-raise as-is
            raise


def set_named_range_value(workbook, range_name, value):
    """Set a value in a named range."""
    # Access the named range and set its value
    workbook.names[range_name].refers_to_range.value = value
    print(f"✅ Set {range_name} = {value}")
    return True


def close_excel_safely(workbook):
    """Close the workbook and Excel application."""
    app = workbook.app
    workbook.close()
    app.quit()
    print("Excel closed successfully")


# Test the first TODO item
if __name__ == "__main__":
    # Open Excel in visible mode so you can see what's happening
    # (macro check happens automatically in access_excel_file)
    workbook = access_excel_file(visible=True)
    print(f"Workbook name: {workbook.name}")
    print(f"Available sheets: {[sheet.name for sheet in workbook.sheets]}")

    # TODO 2: Set value in profileentry cell
    print("\n📝 Setting profileentry = HK117...")
    set_named_range_value(workbook, "profileentry", "HK117")

    # Keep Excel open for inspection
    input("Press Enter to close Excel...")
    close_excel_safely(workbook)

# xlwings visibility options:
# visible=True  - Excel window is visible (default for interactive work)
# visible=False - Excel runs in background (headless mode, faster)
#
# Additional app settings you can use:
# app.display_alerts = False  - Suppress Excel dialog boxes
# app.screen_updating = False - Faster execution, no visual updates
# app.calculation = 'manual'  - Disable automatic calculations

# For headless operation, ensure macros are enabled in Excel Trust Center settings
# or add the project folder to Trusted Locations to avoid ExcelConfigurationError
