import os

import xlwings as xw

# TODO

# [x] Access file ./assets/E-quote v7.0.xlsm (visible=True)
# [x] Set value in cell: profileentry = HK117
# [ ] Click button or execute macro (macro = runme)
# [ ] Get list of values from MATL1entry
# [ ] Set dropdown value: MATL1entry = EPDM


def access_excel_file(visible=True):
    """Access the E-quote Excel file and return the workbook object."""
    # Get the path to the Excel file
    file_path = os.path.join("assets", "E-quote v7.0.xlsm")

    # Check if file exists
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Excel file not found: {file_path}")

    # Create or get Excel app instance with visibility control
    app = xw.App(visible=visible, add_book=False)

    # Open the workbook (this is a .xlsm file with macros)
    wb = app.books.open(file_path)
    print(f"Successfully opened: {file_path}")
    print(f"Excel visible: {app.visible}")

    return wb


def set_named_range_value(workbook, range_name, value):
    """Set a value in a named range."""
    # Access the named range and set its value
    workbook.names[range_name].refers_to_range.value = value
    print(f"✅ Set {range_name} = {value}")
    return True


def close_excel_safely(workbook):
    """Close the workbook and Excel application."""
    app = workbook.app
    workbook.close()
    app.quit()
    print("Excel closed successfully")


# Test the first TODO item
if __name__ == "__main__":
    # Open Excel in visible mode so you can see what's happening
    # (macro check happens automatically in access_excel_file)
    workbook = access_excel_file(visible=True)
    print(f"Workbook name: {workbook.name}")
    print(f"Available sheets: {[sheet.name for sheet in workbook.sheets]}")

    # TODO 2: Set value in profileentry cell
    print("\n📝 Setting profileentry = HK117...")
    set_named_range_value(workbook, "profileentry", "HK117")

    # Keep Excel open for inspection
    input("Press Enter to close Excel...")
    close_excel_safely(workbook)

# xlwings visibility options:
# visible=True  - Excel window is visible (default for interactive work)
# visible=False - Excel runs in background (headless mode, faster)
#
# Additional app settings you can use:
# app.display_alerts = False  - Suppress Excel dialog boxes
# app.screen_updating = False - Faster execution, no visual updates
# app.calculation = 'manual'  - Disable automatic calculations

# For headless operation, ensure macros are enabled in Excel Trust Center settings
# or add the project folder to Trusted Locations
