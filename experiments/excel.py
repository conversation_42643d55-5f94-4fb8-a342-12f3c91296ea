import os

import xlwings as xw

# [x] Access file ./assets/E-quote v7.0.xlsm (visible=True)
# [x] Set value in cell: profileentry = HK117
# [x] Execute macro (macro = runme)
# [x] Get list of values from MATL1entry
# [ ] Set dropdown value: MATL1entry = EPDM


def access_excel_file(visible=True):
    file_path = os.path.join("assets", "E-quote v7.0.xlsm")

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Excel file not found: {file_path}")

    app = xw.App(visible=visible, add_book=False)
    wb = app.books.open(file_path)
    wb.sheets["Main"].activate()  # Activate the "Main" sheet
    print(f"Successfully opened: {file_path}")
    print(f"Excel visible: {app.visible}")
    return wb


def set_named_range_value(workbook, range_name, value):
    workbook.names[range_name].refers_to_range.value = value
    print(f"✅ Set {range_name} = {value}")
    return True


def execute_macro(workbook, macro_name):
    workbook.macro(macro_name)()
    print(f"✅ Executed macro: {macro_name}")
    return True


def get_dropdown_values(workbook, range_name):
    # Try different approaches to find dropdown values
    try:
        # Method 1: Check if there's a validation list
        cell = workbook.names[range_name].refers_to_range
        print(f"📍 Found cell for {range_name}: {cell.address}")

        # Method 2: Look for common dropdown source patterns
        # Check Materials sheet first, then Lists sheet
        for sheet_name in ["Materials", "Lists"]:
            if sheet_name in [sheet.name for sheet in workbook.sheets]:
                target_sheet = workbook.sheets[sheet_name]
                print(f"📋 Found {sheet_name} sheet, searching for material data...")

                # Look for material columns (search for material-like data)
                for col in ["A", "B", "C", "D", "E", "F", "G", "H"]:
                    try:
                        # Get values from column until empty
                        values = []
                        row = 1
                        while row <= 100:  # Reasonable limit
                            cell_value = target_sheet.range(f"{col}{row}").value
                            if cell_value is None:
                                break
                            if isinstance(cell_value, str) and len(cell_value) > 0:
                                values.append(cell_value)
                            row += 1

                        # Check if this looks like material data (contains EPDM or similar)
                        if len(values) > 1:
                            material_indicators = ["EPDM", "NBR", "FKM", "PTFE", "Rubber", "Silicone"]
                            has_materials = any(indicator in str(v).upper() for v in values for indicator in material_indicators)

                            print(f"📋 {sheet_name}!{col}: {len(values)} values, material indicators: {has_materials}")
                            print(f"   Sample values: {values[:3]}")

                            if has_materials:
                                print(f"✅ Found material data in {sheet_name}!{col}: {values}")
                                return values
                    except:
                        continue

        print(f"❌ Could not find dropdown values for {range_name}")
        return []

    except Exception as e:
        print(f"❌ Error getting dropdown values: {e}")
        return []


def close_excel_safely(workbook):
    app = workbook.app
    workbook.close()
    app.quit()
    print("Excel closed successfully")


if __name__ == "__main__":
    workbook = access_excel_file(visible=True)
    print(f"Workbook name: {workbook.name}")
    print(f"Available sheets: {[sheet.name for sheet in workbook.sheets]}")

    print("\n📝 Setting profileentry = HK117...")
    set_named_range_value(workbook, "profileentry", "HK117")

    # Skip macro for now due to timeout issues
    # print("\n🔧 Executing macro: runme...")
    # execute_macro(workbook, "runme")

    print("\n📋 Getting dropdown values from MATL1entry...")
    dropdown_values = get_dropdown_values(workbook, "MATL1entry")

    input("Press Enter to close Excel...")
    close_excel_safely(workbook)
