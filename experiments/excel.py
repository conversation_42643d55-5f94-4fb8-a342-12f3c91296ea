import os

import xlwings as xw

# [x] Access file ./assets/E-quote v7.0.xlsm (visible=True)
# [x] Set value in cell: profileentry = HK117
# [x] Execute macro (macro = runme)
# [x] Get list of values from MATL1entry
# [x] Set dropdown value: MATL1entry = EPDM


def access_excel_file(visible=True):
    file_path = os.path.join("assets", "E-quote v7.0.xlsm")

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Excel file not found: {file_path}")

    app = xw.App(visible=visible, add_book=False)
    wb = app.books.open(file_path)
    wb.sheets["Main"].activate()  # Activate the "Main" sheet
    print(f"Successfully opened: {file_path}")
    print(f"Excel visible: {app.visible}")
    return wb


def set_named_range_value(workbook, range_name, value):
    workbook.names[range_name].refers_to_range.value = value
    print(f"✅ Set {range_name} = {value}")
    return True


def execute_macro(workbook, macro_name):
    workbook.macro(macro_name)()
    print(f"✅ Executed macro: {macro_name}")
    return True


def get_dropdown_values(workbook, range_name):
    try:
        cell = workbook.names[range_name].refers_to_range
        print(f"📍 Found cell for {range_name}: {cell.address}")

        current_value = cell.value
        print(f"📋 Current value in {range_name}: {current_value}")

        # Try to get validation data using COM interface
        try:
            validation = cell.api.Validation
            if hasattr(validation, "Type") and validation.Type == 3:  # xlValidateList
                formula = validation.Formula1
                print(f"📋 Validation formula: {formula}")

                if formula.startswith("="):
                    # Reference to another range
                    source_range = workbook.range(formula[1:])
                    values = [c.value for c in source_range if c.value is not None]
                else:
                    # Direct list of values
                    values = [v.strip() for v in formula.split(",")]

                print(f"✅ Found {len(values)} dropdown values: {values}")
                return values
        except Exception as validation_error:
            print(f"📋 Validation method failed: {validation_error}")

    except Exception as e:
        print(f"❌ Error getting dropdown values: {e}")
        return []


def close_excel_safely(workbook):
    app = workbook.app
    workbook.close()
    app.quit()
    print("Excel closed successfully")


if __name__ == "__main__":
    workbook = access_excel_file(visible=True)
    print(f"Workbook name: {workbook.name}")
    print(f"Available sheets: {[sheet.name for sheet in workbook.sheets]}")

    print("\n📝 Setting profileentry = HK117...")
    set_named_range_value(workbook, "profileentry", "HK117")

    # Skip macro for now due to timeout issues
    # print("\n🔧 Executing macro: runme...")
    # execute_macro(workbook, "runme")

    print("\n📋 Getting dropdown values from MATL1entry...")
    dropdown_values = get_dropdown_values(workbook, "MATL1entry")

    print("\n🔧 Setting MATL1entry = EPDM...")
    if dropdown_values and "EPDM" in dropdown_values:
        set_named_range_value(workbook, "MATL1entry", "EPDM")
    else:
        print(f"❌ EPDM not found in dropdown values: {dropdown_values}")
        print("Setting EPDM anyway...")
        set_named_range_value(workbook, "MATL1entry", "EPDM")

    input("\nPress Enter to close Excel...")
    close_excel_safely(workbook)
