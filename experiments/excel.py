import os

import xlwings as xw

# [x] Access file ./assets/E-quote v7.0.xlsm (visible=True)
# [x] Set value in cell: profileentry = HK117
# [ ] Click button or execute macro (macro = runme)
# [ ] Get list of values from MATL1entry
# [ ] Set dropdown value: MATL1entry = EPDM


def access_excel_file(visible=True):
    file_path = os.path.join("assets", "E-quote v7.0.xlsm")

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Excel file not found: {file_path}")

    app = xw.App(visible=visible, add_book=False)
    wb = app.books.open(file_path)
    wb.sheets["Main"].activate()  # Activate the "Main" sheet
    print(f"Successfully opened: {file_path}")
    print(f"Excel visible: {app.visible}")
    return wb


def set_named_range_value(workbook, range_name, value):
    workbook.names[range_name].refers_to_range.value = value
    print(f"✅ Set {range_name} = {value}")
    return True


def close_excel_safely(workbook):
    app = workbook.app
    workbook.close()
    app.quit()
    print("Excel closed successfully")


if __name__ == "__main__":
    workbook = access_excel_file(visible=True)
    print(f"Workbook name: {workbook.name}")
    print(f"Available sheets: {[sheet.name for sheet in workbook.sheets]}")

    print("\n📝 Setting profileentry = HK117...")
    set_named_range_value(workbook, "profileentry", "HK117")

    input("Press Enter to close Excel...")
    close_excel_safely(workbook)
