import os

import xlwings as xw

# TODO

# [x] Access file ./assets/E-quote v7.0.xlsm
# [ ] Set value in cell: profileentry = HK117
# [ ] Click button or execute macro (macro = runme)
# [ ] Get list of values from MATL1entry
# [ ] Set dropdown value: MATL1entry = EPDM


def access_excel_file():
    """Access the E-quote Excel file and return the workbook object."""
    # Get the path to the Excel file
    file_path = os.path.join("assets", "E-quote v7.0.xlsm")

    # Check if file exists
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Excel file not found: {file_path}")

    # Open the workbook
    try:
        # Open the workbook (this will open Excel if not already running)
        wb = xw.Book(file_path)
        print(f"Successfully opened: {file_path}")
        return wb
    except Exception as e:
        print(f"Error opening Excel file: {e}")
        raise


# Test the first TODO item
if __name__ == "__main__":
    try:
        workbook = access_excel_file()
        print(f"Workbook name: {workbook.name}")
    except Exception as e:
        print(f"Failed to access Excel file: {e}")
