import os

import xlwings as xw

# [x] Access file ./assets/E-quote v7.0.xlsm (visible=True)
# [x] Set value in cell: profileentry = HK117
# [x] Execute macro (macro = runme)
# [ ] Get list of values from MATL1entry
# [ ] Set dropdown value: MATL1entry = EPDM


def access_excel_file(visible=True):
    file_path = os.path.join("assets", "E-quote v7.0.xlsm")

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Excel file not found: {file_path}")

    app = xw.App(visible=visible, add_book=False)
    wb = app.books.open(file_path)
    wb.sheets["Main"].activate()  # Activate the "Main" sheet
    print(f"Successfully opened: {file_path}")
    print(f"Excel visible: {app.visible}")
    return wb


def set_named_range_value(workbook, range_name, value):
    workbook.names[range_name].refers_to_range.value = value
    print(f"✅ Set {range_name} = {value}")
    return True


def execute_macro(workbook, macro_name):
    # Try different ways to call the macro
    try:
        # Method 1: Direct macro call
        workbook.macro(macro_name)()
        print(f"✅ Executed macro: {macro_name}")
        return True
    except Exception as e1:
        print(f"Method 1 failed: {e1}")
        try:
            # Method 2: Call via app
            workbook.app.macro(macro_name)()
            print(f"✅ Executed macro: {macro_name} (via app)")
            return True
        except Exception as e2:
            print(f"Method 2 failed: {e2}")
            # Method 3: Call with full path
            full_macro_name = f"'{workbook.name}'!{macro_name}"
            workbook.app.macro(full_macro_name)()
            print(f"✅ Executed macro: {full_macro_name}")
            return True


def close_excel_safely(workbook):
    app = workbook.app
    workbook.close()
    app.quit()
    print("Excel closed successfully")


if __name__ == "__main__":
    workbook = access_excel_file(visible=True)
    print(f"Workbook name: {workbook.name}")
    print(f"Available sheets: {[sheet.name for sheet in workbook.sheets]}")

    print("\n📝 Setting profileentry = HK117...")
    set_named_range_value(workbook, "profileentry", "HK117")

    print("\n🔧 Executing macro: runme...")
    execute_macro(workbook, "runme")

    input("Press Enter to close Excel...")
    close_excel_safely(workbook)
