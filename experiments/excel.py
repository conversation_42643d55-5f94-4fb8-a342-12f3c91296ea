import os
import threading
import time

import xlwings as xw

# [x] Access file ./assets/E-quote v7.0.xlsm (visible=True)
# [x] Set value in cell: profileentry = HK117
# [x] Execute macro (macro = runme)
# [ ] Get list of values from MATL1entry
# [ ] Set dropdown value: MATL1entry = EPDM


def access_excel_file(visible=True):
    file_path = os.path.join("assets", "E-quote v7.0.xlsm")

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Excel file not found: {file_path}")

    app = xw.App(visible=visible, add_book=False)
    wb = app.books.open(file_path)
    wb.sheets["Main"].activate()  # Activate the "Main" sheet
    print(f"Successfully opened: {file_path}")
    print(f"Excel visible: {app.visible}")
    return wb


def set_named_range_value(workbook, range_name, value):
    workbook.names[range_name].refers_to_range.value = value
    print(f"✅ Set {range_name} = {value}")
    return True


def capture_and_dismiss_msgbox():
    """Capture message box content and dismiss it using AppleScript."""
    import subprocess

    script = """
    tell application "Microsoft Excel"
        try
            set dialogText to ""
            if exists window 1 then
                set windowName to name of window 1
                if windowName contains "Microsoft Excel" then
                    try
                        set dialogText to static text 1 of window 1 as string
                    end try
                    click button "OK" of window 1
                    return dialogText
                end if
            end if
        end try
        return ""
    end tell
    """

    try:
        result = subprocess.run(["osascript", "-e", script], capture_output=True, text=True, timeout=2)
        if result.stdout.strip():
            print(f"📋 Message box captured: {result.stdout.strip()}")
            return result.stdout.strip()
    except Exception as e:
        print(f"Could not capture message box: {e}")

    return ""


def execute_macro(workbook, macro_name):
    try:
        workbook.macro(macro_name)()
        print(f"✅ Executed macro: {macro_name}")

        # Check for and handle any message boxes that appeared
        time.sleep(0.2)  # Brief pause to let dialogs appear
        msg = capture_and_dismiss_msgbox()

        return True
    except Exception as e:
        print(f"❌ Macro execution failed: {e}")
        raise


def close_excel_safely(workbook):
    app = workbook.app
    workbook.close()
    app.quit()
    print("Excel closed successfully")


if __name__ == "__main__":
    workbook = access_excel_file(visible=True)
    print(f"Workbook name: {workbook.name}")
    print(f"Available sheets: {[sheet.name for sheet in workbook.sheets]}")

    print("\n📝 Setting profileentry = HK117...")
    set_named_range_value(workbook, "profileentry", "HK117")

    print("\n🔧 Executing macro: runme...")
    execute_macro(workbook, "runme")

    input("Press Enter to close Excel...")
    close_excel_safely(workbook)
