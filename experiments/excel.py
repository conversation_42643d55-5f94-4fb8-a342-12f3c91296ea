import os

import xlwings as xw

# TODO

# [x] Access file ./assets/E-quote v7.0.xlsm
# [ ] Set value in cell: profileentry = HK117
# [ ] Click button or execute macro (macro = runme)
# [ ] Get list of values from MATL1entry
# [ ] Set dropdown value: MATL1entry = EPDM


def access_excel_file(visible=True):
    """Access the E-quote Excel file and return the workbook object."""
    # Get the path to the Excel file
    file_path = os.path.join("assets", "E-quote v7.0.xlsm")

    # Check if file exists
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Excel file not found: {file_path}")

    # Open the workbook
    try:
        # Create or get Excel app instance with visibility control
        app = xw.App(visible=visible, add_book=False)

        # Optional: Disable alerts and screen updating for better performance
        # app.display_alerts = False
        # app.screen_updating = False

        # Open the workbook
        wb = app.books.open(file_path)
        print(f"Successfully opened: {file_path}")
        print(f"Excel visible: {app.visible}")
        return wb
    except Exception as e:
        print(f"Error opening Excel file: {e}")
        raise


def close_excel_safely(workbook):
    """Safely close the workbook and Excel application."""
    try:
        app = workbook.app
        workbook.close()
        app.quit()
        print("Excel closed successfully")
    except Exception as e:
        print(f"Error closing Excel: {e}")


# Test the first TODO item
if __name__ == "__main__":
    try:
        # Open Excel in visible mode so you can see what's happening
        workbook = access_excel_file(visible=True)
        print(f"Workbook name: {workbook.name}")
        print(f"Available sheets: {[sheet.name for sheet in workbook.sheets]}")

        # Keep Excel open for inspection
        input("Press Enter to close Excel...")
        close_excel_safely(workbook)

    except Exception as e:
        print(f"Failed to access Excel file: {e}")

# xlwings visibility options:
# visible=True  - Excel window is visible (default for interactive work)
# visible=False - Excel runs in background (headless mode, faster)
#
# Additional app settings you can use:
# app.display_alerts = False  - Suppress Excel dialog boxes
# app.screen_updating = False - Faster execution, no visual updates
# app.calculation = 'manual'  - Disable automatic calculations
