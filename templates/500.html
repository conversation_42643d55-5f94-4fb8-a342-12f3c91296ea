{% load static import_map tailwind_tags %}

<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <title>Page Not Found</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <meta name="csrf-token" content="{{ csrf_token }}" />
    <link rel="shortcut icon" href="{% static 'core/img/favicon.ico' %}" />

    {% tailwind_css %}
    <link href="{% static 'core/css/app.css' %}{{ cachebust }}" rel="stylesheet" />
    <link href="{% static 'core/css/htmx-error-handler.css' %}{{ cachebust }}" rel="stylesheet" />

    {% if sentry_enabled %}
      <script src="https://js.sentry-cdn.com/c3ab5b8c716a924b7ef283cf521cd00d.min.js" crossorigin="anonymous"></script>
      <script>
        Sentry.init({
          sendDefaultPii: true,
          release: "{{ version }}",
          environment: "{{ environment }}",
        });
      </script>
    {% endif %}
    <script src="{% static "core/vendor/htmx-2.0.4.js" %}"></script>
    <script src="{% static "core/vendor/htmx-json-enc.js" %}"></script>
    <script src="{% static "core/vendor/flowbite-3.1.2.js" %}"></script>

    {% import_map %}

    <script type="module">
      import { App } from '{% static "core/js/app.js" %}{{ cachebust }}';
      window.app = new App();
    </script>
  </head>

  <body class="bg-gray-100 dark:bg-gray-900 antialiased !p-0 {% block body_classes %}{% endblock %}">
    <section class="flex flex-col items-center py-5 px-8 text-gray-900 dark:text-white">
      {% include "includes/messages.html" %}
      <div class="max-w-2xl justify-center py-10">
        <div class="text-left">
          <div class="text-center">
            <h1 class="text-6xl font-bold text-red-600 dark:text-red-500 mb-4">500</h1>
            <p class="text-2xl mb-6 text-gray-900 dark:text-white">Sorry, an unhandled error occured.</p>
          </div>

          {% if request.user.is_staff %}
            <div class="mb-6 p-5 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
              <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Error Information</h3>
              {% if exception %}
                <p class="mb-2 text-gray-900 dark:text-white"><strong>Exception:</strong> {{ exception }}</p>
              {% endif %}
              <p class="mb-2 text-gray-900 dark:text-white"><strong>URL:</strong> {{ request.build_absolute_uri }}</p>
              <p class="mb-2 text-gray-900 dark:text-white"><strong>Method:</strong> {{ request.method }}</p>
              <p class="mb-2 text-gray-900 dark:text-white"><strong>User:</strong> {{ request.user }}</p>
              <p class="mb-2 text-gray-900 dark:text-white"><strong>Time:</strong> {% now "Y-m-d H:i:s" %}</p>

              {% if traceback %}
                <h4 class="text-md font-semibold mt-4 mb-2 text-gray-900 dark:text-white">Traceback:</h4>
                      <pre class="bg-gray-200 dark:bg-gray-700 p-2 rounded text-sm overflow-x-auto">{{ traceback }}</pre>
              {% endif %}

              {% if context %}
                <h4 class="text-md font-semibold mt-4 mb-2 text-gray-900 dark:text-white">Context Data:</h4>
                      <pre class="bg-gray-200 dark:bg-gray-700 p-2 rounded text-sm overflow-x-auto">{{ context|pprint }}</pre>
              {% endif %}

              {% if debug_info %}
                <h4 class="text-md font-semibold mt-4 mb-2 text-gray-900 dark:text-white">Debug Information:</h4>
                      <pre class="bg-gray-200 dark:bg-gray-700 p-2 rounded text-sm overflow-x-auto">{{ debug_info|pprint }}</pre>
              {% endif %}
            </div>
          {% endif %}

          <div class="mx-auto mb-8 p-5 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <form method="post" action="{% url 'report-error' %}" class="">
              {% csrf_token %}
              <input type="hidden" name="error_type" value="500">
              <input type="hidden" name="url" value="{{ request.build_absolute_uri }}">
              {% if traceback %}
                <input type="hidden" name="traceback" value="{{ traceback|escape }}">
              {% endif %}
              {% if context %}
                <textarea name="context" hidden>{{ context|json|escape }}</textarea>
              {% endif %}

              <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Submit Error Report</h3>
              <p class="mb-4 text-gray-700 dark:text-gray-300">Please describe what you were doing when this error occurred:</p>

              <div class="mb-4">
                <textarea name="description" rows="4" class="w-full px-3 py-2 text-gray-700 border rounded-lg dark:bg-gray-700 dark:text-white dark:border-gray-600" placeholder="I was trying to..."></textarea>
              </div>

              <button type="submit" class="w-full text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                Submit Report
              </button>
            </form>
          </div>

          <div class="flex flex-col md:flex-row justify-center gap-4">
            <a href="{% url 'home' %}" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
              Return to Dashboard
            </a>
            <button onclick="window.history.back()" class="text-gray-900 bg-white border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
              Go Back
            </button>
          </div>
        </div>
      </div>
    </section>
  </body>
</html>