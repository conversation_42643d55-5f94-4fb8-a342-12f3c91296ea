{% load tailwind_tags %}

<!DOCTYPE html>
<html lang="en" class="dark">
    <head>
        <title>Page Not Found</title>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta http-equiv="X-UA-Compatible" content="ie=edge" />
        <link rel="shortcut icon" href="{% static 'core/img/favicon.ico' %}" />

        {% tailwind_css %}
        <link href="{% static 'core/css/app.css' %}{{ cachebust }}" rel="stylesheet" />
        <link href="{% static 'core/css/htmx-error-handler.css' %}{{ cachebust }}" rel="stylesheet" />
    </head>

    <body class="bg-gray-100 dark:bg-gray-900 antialiased !p-0 {% block body_classes %}{% endblock %}">
        <section class="flex flex-col items-center py-5 px-8 text-gray-900 dark:text-white">
            {% include "includes/messages.html" %}

            <div class="max-w-2xl justify-center py-10">
                <div class="text-left">
                    <div class="text-center">
                        <h1 class="text-6xl font-bold text-red-600 dark:text-red-500 mb-4">404</h1>
                        <p class="text-2xl mb-6 text-gray-900 dark:text-white">Sorry, the requested database object could not be found.</p>
                    </div>

                    {% if error_context %}
                        <div class="mb-6 p-5 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                            <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Error Details</h3>
                            <p class="mb-2 text-gray-900 dark:text-white"><strong>Model:</strong> {{ error_context.model_name }}</p>
                            <p class="mb-2 text-gray-900 dark:text-white"><strong>Parameters:</strong></p>
                        <pre class="bg-gray-200 dark:bg-gray-700 p-2 rounded text-sm overflow-x-auto">{{ error_context.params|pprint }}</pre>
                        </div>
                    {% endif %}

                    <div class="flex flex-col md:flex-row justify-center gap-4">
                        <a href="{% url 'home' %}" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            Return to Dashboard
                        </a>
                        <button onclick="window.history.back()" class="text-gray-900 bg-white border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
                            Go Back
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </body>
</html>
