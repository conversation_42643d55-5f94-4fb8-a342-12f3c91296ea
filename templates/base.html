{% load static tz import_map tailwind_tags %}

{% localtime on %}
    <!DOCTYPE html>
    <html lang="en" class="dark">
        <head>
            <title>{% block title %} {% endblock %}</title>
            <meta charset="utf-8" />
            <meta name="viewport" content="width=device-width, initial-scale=1.0" />
            <meta http-equiv="X-UA-Compatible" content="ie=edge" />
            <meta name="csrf-token" content="{{ csrf_token }}" />
            {% block head_meta %} {% endblock %}
            <link rel="shortcut icon" href="{% static 'core/img/favicon.ico' %}" />

            {% tailwind_css %}
            <link href="{% static 'core/css/app.css' %}{{ cachebust }}" rel="stylesheet" />
            <link href="{% static 'core/css/htmx-error-handler.css' %}{{ cachebust }}" rel="stylesheet" />

            {% if sentry_enabled %}
                <script src="https://js.sentry-cdn.com/c3ab5b8c716a924b7ef283cf521cd00d.min.js" crossorigin="anonymous"></script>
                <script>
                    Sentry.init({
                        sendDefaultPii: true,
                        release: "{{ version }}",
                        environment: "{{ environment }}",
                    });
                </script>
            {% endif %}
            <script src="{% static "core/vendor/htmx-2.0.4.js" %}"></script>
            <script src="{% static "core/vendor/htmx-json-enc.js" %}"></script>
            <script src="{% static "core/vendor/flowbite-3.1.2.js" %}"></script>

            {% import_map %}

            <script type="module">
                import { App } from '{% static "core/js/app.js" %}{{ cachebust }}';
                window.app = new App();
            </script>

            {% block head %}{% endblock %}
        </head>

        <body class="bg-gray-100 dark:bg-gray-900 antialiased !p-0 {% block body_classes %}{% endblock %}">
            {% block nav %}
                <nav class="fixed top-0 left-0 z-40 w-64 h-screen border-gray-200 dark:border-gray-700 border-r-1" aria-label="Sidebar">
                    <div class="h-full px-3 py-2 overflow-y-auto bg-gray-50 dark:bg-gray-800 relative">
                        <a href="{% url 'home' %}" class="block inset-x-0 text-gray-900 dark:text-white text-center">
                            <img width="72" height="72" src="{% static 'core/img/hallite-logo-mark.png' %}" class="w-18 block mx-auto mb-2" alt="Hallite logo" />
                            <h2 class="text-xl font-semibold text-gray-900 text-center dark:text-white">Hallite<br />Configurator</h2>
                        </a>
                        <ul class="space-y-2 font-medium border-t border-gray-200 dark:border-gray-700 mt-5 pt-3">
                            <li>
                                <a href="{% url 'home' %}" class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group {% if request.path == '/' %} bg-gray-100 dark:bg-gray-700{% endif %}">
                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" > <path     stroke="currentColor"     stroke-linecap="round"     stroke-linejoin="round"     stroke-width="2"     d="m4 12 8-8 8 8M6 10.5V19a1 1 0 0 0 1 1h3v-3a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v3h3a1 1 0 0 0 1-1v-8.5" /> </svg> <span class="ms-3">Home</span>
                                </a>
                            </li>
                            {% if request.user.is_authenticated and request.user.is_admin %}
                                <li> <a href="{% url "admin:index" %}" target="_blank" class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">     <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z"/> </svg>
                                    <span class="flex-1 ms-3 whitespace-nowrap">Admin</span> </a>
                                </li>
                            {% endif %}
                            {% if request.user.is_authenticated %}
                                <li class="border-t border-gray-200 dark:border-gray-700 pt-2">
                                    <a
                                        href="{% url 'signout' %}"
                                        hx-post="{% url 'signout' %}"
                                        hx-target="body"
                                        hx-swap="outerHTML"
                                        class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group"
                                    > <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" > <path     stroke="currentColor"     stroke-width="2"     d="M7 17v1a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-1a3 3 0 0 0-3-3h-4a3 3 0 0 0-3 3Zm8-9a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" /> </svg> <span class="flex-1 ms-3 whitespace-nowrap">Sign Out</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                        <div class="absolute inset-x-0 bottom-4 text-gray-900 dark:text-white text-center">
                            <p class="text-sm">Version {{ version }}<br/>Environment: {{ environment }}</p>
                        </div>
                    </div>
                </nav>
            {% endblock nav %}

            {% block header_parent %}
                <div class="ml-64">
                    <header class="px-5 border-gray-200 bg-gray-50 dark:bg-gray-800 dark:border-gray-700 border-b-1">
                        <div class="flex items-center py-4">
                            <h1
                                class="flex-1 text-2xl font-semibold whitespace-nowrap text-gray-900 dark:text-white"
                            >
                                {% block page_title %}Page Title{% endblock %}
                            </h1>
                            <div class="flex-no-wrap items-center">
                                {% block header %}{% endblock %}
                                <button id="theme-toggle" type="button" class="align-top text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2">
                                    <svg id="theme-toggle-dark-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path></svg>
                                    <svg id="theme-toggle-light-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path></svg>
                                </button>
                            </div>
                        </div>
                    </header>
                </div>
            {% endblock header_parent %}

            <section class="w-full h-full py-5 px-8 text-gray-900 dark:text-white">
                {% include "includes/messages.html" %}
                {% block content %}{% endblock %}
            </section>
            {% block body_end %}{% endblock %}
        </body>
    </html>
{% endlocaltime %}