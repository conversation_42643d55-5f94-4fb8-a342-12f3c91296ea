{% extends "base.html" %}
{% load static %}

{% block title %}Hallite Configurator{% endblock %}
{% block page_title %}Hallite Configurator{% endblock %}

{% block header_parent %}{% endblock %}
{% block nav %}{% endblock %}

{% block content %}
  <section class="bg-gray-100 dark:bg-gray-900">
    <div class="flex flex-col items-center px-6 py-8 mx-auto sm:h-screen lg:py-0">
      <div class="w-3/4 max-w-[600px] bg-white rounded-lg shadow border-gray-200 dark:border md:mt-20 xl:p-0 dark:bg-gray-800 dark:border-gray-700">
        <div class="p-6 space-y-4 md:space-y-6 sm:p-8">
          <h1 class="text-xl text-center font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white">
            <img src="{% static 'core/img/hallite-logo-mark.png' %}" class="w-12 block mx-auto mb-2" alt="Hallite logo" />
            <span>Hallite Configurator Sign In</span>
          </h1>


          <form class="space-y-4 md:space-y-6" action="{% url 'signin' %}" method="post">
            {% csrf_token %}
            <div>
              <label for="username"
                     class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Username</label>
              <input type="text"
                     name="username"
                     id="username"
                     class="bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                     placeholder="Enter your username"
                     required>
            </div>
            <div>
              <label for="password"
                     class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Password</label>
              <input type="password"
                     name="password"
                     id="password"
                     placeholder="••••••••"
                     class="bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                     required>
            </div>
            <div class="flex items-center justify-between">
              {% component "Link" url='{% url "admin_password_reset" %}' %}Forgot password?{% endcomponent %}
            </div>
            <button type="submit" class=" text-white bg-blue-700 hover:bg-blue-800 font-medium rounded-lg text-sm px-5 py-2 me-2 dark:bg-blue-600 dark:hover:bg-blue-700 hover:cursor-pointer">Sign In</button>
          </form>
        </div>
      </div>
    </div>
  </section>
{% endblock %}

{% block body_end %}
  <script src="{% static 'core/js/ports.js' %}" type="module"></script>
  <script type="module">
    import { Ports } from '{% static "core/js/ports.js" %}';

    document.addEventListener('DOMContentLoaded', ()=> {
      Ports.checkAssigned();
      document.querySelector("#username").focus();
    });
  </script>
{% endblock %}
