{% extends "admin_interface:admin/base_site.html" %}
{% load static import_map %}

{% block extrahead %}
  {{ block.super }}
  <meta name="csrf-token" content="{{ csrf_token }}" />

  <link rel="stylesheet" href="{% static "core/css/admin.css" %}">
  <link rel="stylesheet" href="{% static "core/css/htmx-error-handler.css" %}{{ cachebust }}">
  <script src="{% static "core/vendor/htmx-2.0.4.js" %}"></script>
  {% import_map %}
  <script type="module">
    import { Admin } from '{% static "core/js/admin.js" %}{{ cachebust }}';
    window.admin = new Admin();
  </script>
{% endblock %}

