FROM python:3.13 AS builder
LABEL maintainer="<PERSON> <<EMAIL>>"

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    UV_CACHE_DIR=/root/.cache/uv \
    UV_COMPILE_BYTECODE=1 \
    UV_FROZEN=1 \
    UV_LINK_MODE=copy \
    UV_NO_MANAGED_PYTHON=1 \
    UV_PROJECT_ENVIRONMENT=/venv \
    UV_PYTHON_DOWNLOADS=never \
    UV_REQUIRE_HASHES=1 \
    UV_VERIFY_HASHES=1 \
    VIRTUAL_ENV=/venv \
    PATH="/venv/bin:$PATH"

COPY --from=ghcr.io/astral-sh/uv:latest /uv /usr/local/bin/uv

WORKDIR /usr/src/app

RUN apt-get update && \
    apt-get upgrade --yes --no-install-recommends --no-install-suggests && \
    curl -fsSL https://deb.nodesource.com/setup_22.x | bash - && \
    apt-get update && apt-get install -y nodejs && \
    npm install -g npm@latest

RUN --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv venv $VIRTUAL_ENV && \
    uv sync --no-install-project --no-editable --no-dev

# Copy what's needed for tailwind build and collectstatic
COPY core/ core/
COPY config/ ./config/
COPY components/ ./components/
COPY templates/ ./templates/
COPY theme/ ./theme/
COPY manage.py .

RUN uv run manage.py tailwind install && \
    uv run manage.py tailwind build && \
    DEBUG=false uv run manage.py collectstatic --no-input -i node_modules -i source.css

FROM python:3.13-slim

ARG UID=1000
ARG GID=1000

ENV VIRTUAL_ENV=/venv \
    UV_PROJECT_ENVIRONMENT=/venv \
    UV_CACHE_DIR=/home/<USER>/.cache/uv \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/venv/bin:$PATH"

WORKDIR /usr/src/app

RUN apt-get update && \
    apt-get install --yes --no-install-recommends --no-install-suggests --fix-missing \
    sqlite3 \
    wget \
    bash \
    nano \
    ca-certificates \
    sudo && \
    apt-get autoremove -y && \
    apt-get clean -y && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

RUN groupadd -g "${GID}" hallite && \
    useradd hallite \
        --create-home \
        --shell=/bin/bash \
        --no-log-init \
        --uid "${UID}" \
        --gid "${GID}" && \
    echo "hallite ALL=(ALL) NOPASSWD:ALL" >>/etc/sudoers.d/nopasswd && \
    chown hallite:hallite -R /usr/src/app && \
    echo '\neval "$(just --completions bash)"\n' >> /home/<USER>/.bashrc && \
    wget -q https://raw.githubusercontent.com/django/django/main/extras/django_bash_completion -O /home/<USER>/.django_bash_completion && \
    echo '\nsource ~/.django_bash_completion\n' >> /home/<USER>/.bashrc

COPY --chown=hallite:hallite . .
COPY --link --from=builder --chown=hallite:hallite /venv /venv
COPY --link --from=builder --chown=hallite:hallite /usr/src/app/static /usr/src/app/static
COPY --link --from=builder --chown=hallite:hallite /usr/src/app/theme/static /usr/src/app/theme/static
COPY --from=ghcr.io/astral-sh/uv:latest /uv /usr/local/bin/uv

ARG VERSION
ARG COMMIT
ENV VERSION=${VERSION} \
    COMMIT=${COMMIT}

USER hallite

ENTRYPOINT ["/usr/src/app/bin/entrypoint.sh"]

EXPOSE 8080

CMD ["gunicorn", "-c", "python:config.gunicorn", "config.wsgi"]