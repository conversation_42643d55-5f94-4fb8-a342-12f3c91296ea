Option Explicit
'
'
'
'

Dim profile As String
Dim ODreqd As Double
Dim ODchk As Double
Dim ODmatch As Double
Dim ODfinal As Double
Dim IDreqd As Double
Dim IDchk As Double
Dim IDmatch As Double
Dim IDfinal As Double
Dim IDstop As Double
Dim MaterialNo As Integer
Dim MATPART As String
Dim matpartchk As String
Dim costpermmmatch As Double
Dim costpermmfinal As Double
Dim check1 As Double
Dim check2 As Double
Dim instock1 As String
Dim instock2 As String
Dim instock3 As String
Dim drawing As String
Dim prepro As String
Dim protectme As Boolean
Dim EmailSubject As String
Dim message As String
Dim title As String
Dim default As String
Dim partno As String
Dim summaryno As String


Sub auto_open()

Sheets("Main").Select
Range("profileentry").Select

End Sub

Sub runme()
     
protectme = True
If Range("protectme") = "x" Then protectme = False
If Range("protectme") = "X" Then protectme = False

Sheets("Main").Select

ActiveSheet.Unprotect ("poly")
ActiveWorkbook.Unprotect ("boofhead")

Sheets("Calculations").Visible = True
Sheets("Profile Data").Visible = True
Sheets("Billets").Visible = True
Sheets("Springs").Visible = True
Sheets("Lists").Visible = True
Sheets("BS-Orings").Visible = True
Sheets("log of chgs").Visible = True

Range("refreshdata") = ""
Range("matlsizena") = ""
profile = Range("profile")

Sheets("Main").Select
Range("fuckup") = ""

If Range("ODreqd") > 600 Then
    Range("fuckup") = "error"
    GoTo endofrunme
End If

If Range("IDreqd") < 3 Then
    If profile = "EP116" Then GoTo bypassIDreqd
    If profile = "EP216" Then GoTo bypassIDreqd
    Range("fuckup") = "error"
End If

bypassIDreqd:

If Range("IDreqd") > Range("ODreqd") Then
    Range("fuckup") = "error"
    GoTo endofrunme
End If

dwgremove   'removes profile drawing
update      'updates profile drawing
findprice

endofrunme:

Range("partdesc") = Range("partdesccalc")
If IDreqd + 2 < 3 Then Range("partdesc") = "ID too small. Check with Technical."
If profile = "EP116" Then Range("partdesc") = Range("partdesccalc")
If profile = "EP116" Then Range("partdesc") = Range("partdesccalc")

Application.ScreenUpdating = False
history

Application.ScreenUpdating = True
Sheets("Main").Select

If protectme = True Then
    Sheets("BS-Orings").Visible = False
    Sheets("Lists").Visible = False
    Sheets("Springs").Visible = False
    Sheets("Billets").Visible = False
    Sheets("Profile Data").Visible = False
    Sheets("Calculations").Visible = False
    Sheets("Billets ODBC").Visible = False

    ActiveWorkbook.Protect ("boofhead")
    ActiveSheet.Protect ("poly")
End If

Range("profileentry").Select

End Sub

Sub dwgremove()

Sheets("Main").Select
Range("profileold").Select
drawing = "'" & ActiveCell.Value
ActiveSheet.Shapes(drawing).Select
Selection.Delete

End Sub

Sub update()

Application.ScreenUpdating = False
drawing = "'" & Range("profileentry")
Sheets("Profiles").Select
ActiveSheet.Shapes(drawing).Select
Selection.Copy
Sheets("Main").Select
Range("dwgcell").Select
ActiveSheet.Paste
Range("profileold") = drawing

Application.ScreenUpdating = True

End Sub

Sub findprice()

Sheets("Calculations").Select
If Range("MATL1") <> "" Then
    MaterialNo = 1
    findprice1
End If
If Range("MATL2") <> "" Then
    MaterialNo = 2
    findprice1
End If
If Range("MATL3") <> "" Then
    MaterialNo = 3
    findprice1
End If

End Sub

Sub findprice1()

'find billet and price for Materials 1,2,3

Application.ScreenUpdating = False
ODreqd = Range("ODADJ" & MaterialNo)
IDreqd = Range("IDADJ" & MaterialNo)
MATPART = Range("MATL" & MaterialNo)
profile = Range("profile")
Sheets("Billets").Select
Range("A2").Select
   
IDfinal = -1
ODfinal = -1
IDstop = -1
costpermmfinal = 999999
  
matcheck1:   ' finds the correct billet material
    If ActiveCell.Value = "" Then
'        MsgBox ("Material/Size not available. Contact Evco")
        Sheets("Calculations").Select
        Range("matlsizena") = "y"
        GoTo Neilisapommiebastard1
    End If
    If ActiveCell.Value <> MATPART Then
        Selection.Offset(1, 0).Range("A1").Select
        GoTo matcheck1:
        Else:
            GoTo startofcheck1
    End If
 
startofcheck1:
matpartchk = ActiveCell.Value
Selection.Offset(0, 1).Range("A1").Select
IDchk = ActiveCell.Value
Selection.Offset(0, 1).Range("A1").Select
ODchk = ActiveCell.Value

'If IDstop = IDchk Then GoTo endofcheck1

If matpartchk = MATPART Then
    If IDchk <= IDreqd Then
        If ODchk >= ODreqd Then
            IDstop = IDchk
            IDmatch = IDchk
            ODmatch = ODchk
            Selection.Offset(0, 1).Range("A1").Select
            costpermmmatch = ActiveCell.Value
            Selection.Offset(0, -1).Range("A1").Select
            
            'replace billet if a cheaper costpermm is found
            If costpermmmatch < costpermmfinal Then
                
                'exclude billet if it has a zero costpermm
                If costpermmmatch = 0 Then GoTo endofcheck1

                costpermmfinal = costpermmmatch
                IDfinal = IDmatch
                ODfinal = ODmatch
            End If
     
        End If
    End If
End If
endofcheck1:
Selection.Offset(1, -2).Range("A1").Select

'check if another ID and OD are more suitable
If IDreqd < IDchk Then GoTo findprice1
GoTo startofcheck1

findprice1:
   
If IDfinal = -1 Then
    Sheets("Calculations").Select
    Range("matlsizena") = "y"
    Range("costpermm" & MaterialNo) = "-"
    Range("BilletOD" & MaterialNo) = "-"
    Range("BilletID" & MaterialNo) = "-"
Else:
    Range("costpermm" & MaterialNo) = costpermmfinal
    Range("BilletOD" & MaterialNo) = ODfinal
    Range("BilletID" & MaterialNo) = IDfinal
End If
    
Neilisapommiebastard1:
End Sub

Sub awesome()
MsgBox "Fookin nice, eh?"
End Sub

Sub donttouchmethere()
MsgBox ("Good value, isn't it!")
End Sub

Sub Send_Selection_Or_ActiveSheet_with_MailEnvelope()

Dim Sendrng As Range

On Error GoTo StopMacro
    
Sheets("Main").Select
ActiveSheet.Unprotect ("poly")
Range("A1:G27").Select

message = "Enter a reference, such as PO#."    ' Set prompt.
title = "Email subject"    ' Set title.
default = "PO "    ' Set default.
' Display message, title, and default value.
EmailSubject = InputBox(message, title, default)

With Application
    .ScreenUpdating = False
    .EnableEvents = False
End With

'Note: if the selection is one cell it will send the whole worksheet
Set Sendrng = Selection

'Create the mail and send it
With Sendrng

    ActiveWorkbook.EnvelopeVisible = True
    With .Parent.MailEnvelope

        ' Set the optional introduction field thats adds
        ' some header text to the email body.
        .Introduction = " "

        ' In the "With .Item" part you can add more options
        ' See the tips on this Outlook example page.
        ' http://www.rondebruin.nl/mail/tips2.htm
        With .Item
            .To = "<EMAIL>"
            .Subject = "E-Quote " & EmailSubject
            .Send
        End With

    End With
End With

ActiveSheet.Protect ("poly")

Range("C6").Select

StopMacro:
    With Application
        .ScreenUpdating = True
        .EnableEvents = True
    End With
    ActiveWorkbook.EnvelopeVisible = False

End Sub

Sub history()
'
' runs after runme
If Range("partdesc") = "Material/Size not available" Then
    MsgBox ("Material/Size not available. Part not added to Entry History")
    GoTo endofmassentry
End If

    Sheets("Entry History").Select
    Range("A4").Select

nextline:
    If ActiveCell.Value <> "" Then
        Selection.Offset(1, 0).Range("A1").Select
        GoTo nextline
    End If
    
    ActiveCell.FormulaR1C1 = "=ROW()-3"
    partno = ActiveCell.Value
    Selection.ClearContents

    Sheets("Main").Select
    Range("profileentry").Copy
    Sheets("Entry History").Select
    Selection.PasteSpecial Paste:=xlPasteValues
    
    Sheets("Main").Select
    Range("measureentry").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues
        
    Sheets("Main").Select
    Range("ODentry").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues
       
    Sheets("Main").Select
    Range("IDentry").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues
       
    Sheets("Main").Select
    Range("CHentry").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues
    
'extra dimensions
If Range("extratype") = "" Then
    Selection.Offset(0, 2).Range("A1").Select
    GoTo endofextra
End If
    Sheets("Main").Select
    Range("extratype").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues
    
    Sheets("Main").Select
    Range("extraentry").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues
endofextra:

'Type 1
    Sheets("Main").Select
    Range("Type1").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues
 
    Sheets("Main").Select
    Range("MATL1entry").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues
 
    Sheets("Main").Select
    Range("billetreqd1").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues

    Sheets("Main").Select
    Range("OALPART1").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues

    Sheets("Main").Select
    Range("minreqd1").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues

'Type 2
If Range("type2") = "" Then
    Selection.Offset(0, 5).Range("A1").Select
    GoTo endoftype2
End If
    Sheets("Main").Select
    Range("Type2").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues
 
    Sheets("Main").Select
    Range("MATL2entry").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues
 
    Sheets("Main").Select
    Range("billetreqd2").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues

    Sheets("Main").Select
    Range("OALPART2").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues

    Sheets("Main").Select
    Range("minreqd2").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues
endoftype2:

'Type 3
If Range("type3") = "" Then
    Selection.Offset(0, 5).Range("A1").Select
    GoTo endoftype3
End If

    Sheets("Main").Select
    Range("Type3").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues
 
    Sheets("Main").Select
    Range("MATL3entry").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues
 
    Sheets("Main").Select
    Range("billetreqd3").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues

    Sheets("Main").Select
    Range("OALPART3").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues

    Sheets("Main").Select
    Range("minreqd3").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues
endoftype3:

    Sheets("Main").Select
    Range("partdesc").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues

    Sheets("Main").Select
    Range("costpriceevco").Copy
    Sheets("Entry History").Select
    Selection.Offset(0, 1).Range("A1").Select
    Selection.PasteSpecial Paste:=xlPasteValues

    Range("A4").Select
    
    summarize
    
'    MsgBox ("Part " & partno & " has been entered and summarized.")
    Sheets("Main").Select

endofmassentry:

End Sub

Sub summarize()

    Sheets("Entry History").Select
'Type1
    If Range("J" & partno + 3) <> "" Then
        Range("J" & partno + 3 & ":L" & partno + 3).Copy
        Range("Z4").Select
  
nextlinesummary1:
        If ActiveCell.Value <> "" Then
            Selection.Offset(1, 0).Range("A1").Select
            GoTo nextlinesummary1
        End If
        ActiveSheet.Paste
    End If
    
'Type2
    If Range("O" & partno + 3) <> "" Then
        Range("O" & partno + 3 & ":Q" & partno + 3).Copy
        Range("Z4").Select
  
nextlinesummary2:
        If ActiveCell.Value <> "" Then
            Selection.Offset(1, 0).Range("A1").Select
            GoTo nextlinesummary2
        End If
        ActiveSheet.Paste
    End If
    
'Type3
    If Range("T" & partno + 3) <> "" Then
        Range("T" & partno + 3 & ":V" & partno + 3).Copy
        Range("Z4").Select
  
nextlinesummary3:
        If ActiveCell.Value <> "" Then
            Selection.Offset(1, 0).Range("A1").Select
            GoTo nextlinesummary3
        End If
        ActiveSheet.Paste
    End If

    Selection.Offset(1, 0).Range("A1").Select
    ActiveCell.FormulaR1C1 = "=ROW()-1"
    summaryno = ActiveCell.Value
    Selection.ClearContents
        

End Sub

Sub deletemassentryrows()

deletemassentryrows:
    Sheets("Entry History").Select
    Rows("4:10000").Select
    Selection.Delete Shift:=xlUp
    If Range("a4") <> "" Then GoTo deletemassentryrows
    Range("A4").Select
    Sheets("Main").Select

End Sub

