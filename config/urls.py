from django.conf import settings
from django.contrib import admin
from django.contrib.auth import views as auth_views
from django.urls import include, path, re_path
from django.views.static import serve

urlpatterns = [
    path("", include("django_components.urls")),
    path("", include("core.urls")),
    path(
        "manage/password_reset/",
        auth_views.PasswordResetView.as_view(extra_context={"site_header": admin.site.site_header}),
        name="admin_password_reset",
    ),
    path(
        "manage/password_reset/done/",
        auth_views.PasswordResetDoneView.as_view(extra_context={"site_header": admin.site.site_header}),
        name="password_reset_done",
    ),
    path(
        "reset/<uidb64>/<token>/",
        auth_views.PasswordResetConfirmView.as_view(extra_context={"site_header": admin.site.site_header}),
        name="password_reset_confirm",
    ),
    path(
        "reset/done/",
        auth_views.PasswordResetCompleteView.as_view(extra_context={"site_header": admin.site.site_header}),
        name="password_reset_complete",
    ),
    path("admin/dynamic_raw_id/", include("dynamic_raw_id.urls")),
    path("manage/", admin.site.urls),
    re_path(r"^media/(?P<path>.*)$", serve, {"document_root": settings.MEDIA_ROOT}),
]

if settings.TEST_ERROR_VIEWS_ENABLED:
    urlpatterns += [
        path("test-error/", include("core.test_error_urls")),
    ]

if settings.ENV == "development":
    if settings.DEBUG and settings.DEBUG_TOOLBAR_ENABLED:
        urlpatterns.append(path("__debug__/", include("debug_toolbar.urls")))

    if settings.BROWSER_RELOAD_ENABLED:
        urlpatterns.append(path("__reload__/", include("django_browser_reload.urls")))

handler404 = "core.views.handler404"
handler500 = "core.views.handler500"
