{"name": "theme", "version": "4.0.1", "description": "", "scripts": {"start": "npm run dev", "build": "npm run build:clean && npm run build:tailwind", "build:clean": "rimraf ../static/css/dist", "build:tailwind": "cross-env NODE_ENV=production postcss ./src/source.css -o ../static/css/dist/styles.css --minify", "dev": "cross-env NODE_ENV=development postcss ./src/source.css -o ../static/css/dist/styles.css --watch"}, "keywords": [], "author": "", "license": "MIT", "devDependencies": {"@tailwindcss/postcss": "^4.1.0", "cross-env": "^7.0.3", "postcss": "^8.5.3", "postcss-cli": "^11.0.1", "postcss-nested": "^7.0.2", "postcss-simple-vars": "^7.0.1", "rimraf": "^6.0.1", "tailwindcss": "^4.1.0"}, "dependencies": {"flowbite": "^3.1.2", "postcss-import": "^16.1.0"}}