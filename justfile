#!/usr/bin/env just

set shell := ["bash", "-c"]

default:
    @just --list

#####################
# LOCAL DEVELOPMENT #
#####################

dev-venv:
    -rm -fr .venv
    uv venv
    uv pip install --group dev --group test --compile-bytecode
    rm -fr hallite_configurator.egg-info build

dev-dependencies:
    uv pip install --group dev --group test --compile-bytecode
    rm -fr hallite_configurator.egg-info build

dev-env-file:
    doppler secrets download --no-file --format docker > .env

dev-remove-envfile:
    rm .env

dev-get-tls:
	doppler secrets get TLS_CERT --plain > tls/cert.pem
	doppler secrets get TLS_KEY --plain > tls/key.pem    

dev-server:
    doppler run -- uv run manage.py runserver_plus [::]:8080

dev-port-sync:
    doppler run -- uv run manage.py sync_port_status

dev-docs:
    mkdocs serve --dev-addr 0.0.0.0:8000

##########
# DOCKER #
##########

docker-build:
    #!/usr/bin/env bash
    set -e
    docker pull python:3.13
    docker pull python:3.13-slim
    VERSION=$(just git-version)
    echo "Building with VERSION=${VERSION}"
    docker buildx build --build-arg VERSION="${VERSION}" --build-arg COMMIT="$(git rev-parse HEAD)" -t ghcr.io/ryan-blunden/hallite-configurator:${VERSION} -t ghcr.io/ryan-blunden/hallite-configurator:latest .

docker-compose-up:
    @trap 'rm -f .env; exit' INT TERM; \
    doppler secrets download --no-file --format docker > .env && \
    docker compose up; \
    rm -f .env

docker-compose-up-detach:
    doppler secrets download --no-file --format docker > .env
    docker compose up --detach
    rm .env

docker-compose-down:
    doppler secrets download --no-file --format docker > .env
    docker compose down
    rm -f .env

docker-compose-reset:
    @trap 'rm -f .env; exit' INT TERM; \
    doppler secrets download --no-file --format docker > .env && \
    docker compose down -v; \
    rm -f .env

[positional-arguments]
docker-app-cmd *args='':
    @docker exec -it app "$@"

docker-shell:
    just docker-app-cmd bash

docker-image-versions:
    @open https://github.com/ryan-blunden/hallite-configurator/pkgs/container/hallite-configurator


#######
# APP #
#######

app-install-dependencies:
    uv pip install -e .
    uv lock
    rm -fr hallite_configurator.egg-info build

app-upgrade-dependencies:
    uv lock --upgrade

pre-commit:
    uv run pre-commit run --all-files

app-dump-data app model:
    uv run manage.py dumpdata {{app}}.{{model}} --format=yaml --indent=2 > {{app}}/fixtures/{{model}}.yaml

app-load-base-data:
    uv run manage.py loaddata core/fixtures/group.yaml
    uv run manage.py loaddata core/fixtures/user.yaml
    uv run manage.py loaddata core/fixtures/admin_interface.yaml

git-version:
    #!/usr/bin/env bash
    set -e
    # Try to get the exact tag for the current commit
    TAG=$(git describe --tags --exact-match HEAD 2>/dev/null || echo "")

    if [ -n "$TAG" ]; then
        # Strip leading 'v' if present
        echo "${TAG#v}"
    else
        # Fallback to branch-commit if no exact tag is found
        BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
        COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
        echo "${BRANCH}-${COMMIT}"
    fi


##########
# DJANGO #
##########

django-collectstatic:
   uv run manage.py tailwind build
   uv run manage.py collectstatic --no-input -i node_modules -i source.css



##########
# CHECKS #
##########

check:
    uv run just check-black
    uv run just check-isort
    uv run just check-flake8
    uv run just check-pylint
    uv run just check-djhtml
    uv run just check-js-css
    uv run just check-autoflake
    uv run just check-types

check-djlint:
    uv run djlint ./ --check

check-flake8:
    uv run flake8 ./

check-isort:
    uv run isort ./ --check

check-black:
    uv run black ./ --check

check-dockerfile:
    docker run --rm -i hadolint/hadolint hadolint - < "Dockerfile"

check-pylint:
    uv run pylint --load-plugins pylint_django ./

check-djhtml:
    uv run djhtml core config templates -c

check-js-css:
    cd core/static/core && npm run lint-fix

check-autoflake:
    uv run autoflake --check --quiet --recursive ./

check-types:
    uv run ty check


##########
# FORMAT #
##########

format:
    uv run just format-black
    uv run just format-isort
    uv run just format-djhtml
    uv run just format-js-css
    uv run just format-autoflake

format-isort:
    uv run isort ./

format-black:
    uv run black ./

format-djhtml:
    uv run djhtml core config templates

format-js-css:
    cd core/static/core && npm run format

format-autoflake:
    uv run autoflake --in-place --recursive ./


###########
# TESTING #
###########

# Run tests with default settings from pyproject.toml
test *args='':
    uv run pytest -v "{{ args }}"

# Run tests with debug logging
test-verbose *args='':
    uv run pytest --log-cli-level=DEBUG "{{ args }}"

# Run tests with coverage reporting
test-coverage:
    uv run pytest --log-cli-level=DEBUG --cov --cov-config=pyproject.toml --cov-report=term --cov-report=html


##########
# CI/CD #
#########

github-latest-build-job:
    open "$(gh run list --workflow docker-build-push.yaml --limit 1 --json url --jq '.[0].url')"

tag-release:
    #!/usr/bin/env bash
    set -e
    echo "Checking current branch..."
    BRANCH=$(git rev-parse --abbrev-ref HEAD)
    if [ "$BRANCH" != "main" ]; then
        echo "Not on main branch. Current branch is $BRANCH.";
        exit 1;
    fi
    echo "Checking for uncommitted changes..."
    if ! git diff --quiet HEAD; then
        echo "There are uncommitted changes.";
        exit 1;
    fi
    echo "Checking for staged changes..."
    if ! git diff --cached --quiet; then
        echo "There are staged changes.";
        exit 1;
    fi
    read -p "Enter version (last $(git describe --tags --abbrev=0)): " VERSION;

    echo "Checking if tag v$VERSION already exists..."
    if [ -n "$(git tag -l "v$VERSION")" ]; then
        echo "Error: Tag v$VERSION already exists."
        # Optional: Revert pyproject.toml change if tag exists and we are aborting
        # git checkout -- pyproject.toml
        exit 1;
    fi

    echo "Locking dependencies with uv..."
    uv lock

    echo "Tagging release v$VERSION..."
    git tag "v$VERSION"

    echo "Pushing tag v$VERSION to origin..."
    git push origin "v$VERSION"
