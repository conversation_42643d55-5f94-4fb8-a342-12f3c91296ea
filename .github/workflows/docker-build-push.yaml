name: <PERSON>uild and Push Docker Image
permissions:
  contents: read
  packages: write

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

jobs:
  test:
    uses: ./.github/workflows/test.yaml
    secrets: inherit
  docker-build-push:
    runs-on: ubuntu-latest
    needs: [test]
    if: ${{ needs.test.outputs.test_success == 'true' }}
    steps:
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Checkout
        uses: actions/checkout@v4

      - name: Extract Version
        id: version
        run: |
          echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_ENV

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          buildkitd-flags: --debug

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GH_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64
          push: true
          tags: |
            ghcr.io/ryan-blunden/hallite-configurator:${{ env.VERSION }}
            ghcr.io/ryan-blunden/hallite-configurator:latest
          build-args: |
            VERSION=${{ env.VERSION }}
            COMMIT=${{ github.sha }}
          labels: |
            org.opencontainers.image.source=${{ github.event.repository.html_url }}
            org.opencontainers.image.revision=${{ github.sha }}
          cache-from: type=gha,scope=${{ github.workflow }}
          cache-to: type=gha,mode=max,scope=${{ github.workflow }}
          secrets: |
            GITHUB_TOKEN=${{ secrets.GH_TOKEN }}
