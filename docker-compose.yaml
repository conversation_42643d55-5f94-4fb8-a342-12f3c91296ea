services:
  app:
    image: ghcr.io/ryan-blunden/hallite-configurator
    container_name: hallite-configurator
    develop:
      watch:
        - path: .
          target: /usr/src/app
          action: sync
    stdin_open: true
    tty: true
    ports:
      - 8080:8080
    volumes:
      - ./db:/usr/src/app/db
      - ./uploads:/usr/src/app/uploads
    env_file:
      - .env
    restart: always
    stop_grace_period: 3s
    healthcheck:
      test: ["CMD", "wget", "--no-check-certificate", "-q", "-O", "-", "http://localhost:8080/health-check/"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 60s

volumes:
  hallite: {}
  venv: {}
